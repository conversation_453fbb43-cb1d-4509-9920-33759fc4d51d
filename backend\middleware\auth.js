/**
 * Middleware de autenticação
 * Verifica se o token JWT é válido e adiciona o usuário ao request
 */
const fp = require('fastify-plugin');

const authenticate = async (fastify) => {
  fastify.decorate('authenticate', async (request, reply) => {
    try {
      await request.jwtVerify();
    } catch (err) {
      reply.code(401).send({ error: 'Não autorizado' });
    }
  });
};

module.exports = fp(authenticate);