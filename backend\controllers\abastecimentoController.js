const { executeQuery } = require('../models/db');

/**
 * Controlador de Abastecimento
 * Gerencia registros de abastecimento de veículos
 */
const abastecimentoController = {
  /**
   * Registra abastecimento de veículo
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados do abastecimento registrado
   */
  registrarAbastecimento: async (request, reply) => {
    const {
      placa,
      litros,
      encerrante,
      observacao
    } = request.body;

    try {
      // Log para verificar os dados recebidos
      request.log.info({
        message: 'Dados recebidos para abastecimento',
        placa,
        litros,
        encerrante,
        observacao
      });

      // Buscar o ID do veículo na tabela Frota
      const veiculoQuery = `
        SELECT Id FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }

      const veiculoId = veiculos[0].Id;

      // Verificar se o veículo está no pátio
      const veiculoPatioQuery = `
        SELECT TOP 1 Id FROM MovFrota
        WHERE Veiculo = @veiculoId
        AND DataSaida IS NULL
        ORDER BY DataEntrada DESC
      `;

      const veiculosPatio = await executeQuery(veiculoPatioQuery, { veiculoId });

      if (veiculosPatio.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no pátio'
        });
      }

      const movimentacaoId = veiculosPatio[0].Id;

      // Obter o ID do usuário atual a partir do token JWT
      const usuarioId = request.user.id;

      // Atualizar registro na MovFrota com dados de abastecimento
      // Usar o fuso horário do Brasil (UTC-3)
      const updateQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
        DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
        DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

        UPDATE MovFrota SET
          Litragem = @litros,
          StatusPosto = '1',
          UsuarioPosto = @usuarioId,
          DataPosto = @dataHoraBrasil,
          HoraPosto = @horaFormatada,
          Encerrante = @encerrante
        WHERE Id = @movimentacaoId;

        SELECT
          m.Id,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          m.DataEntrada,
          m.Litragem,
          m.StatusPosto,
          m.DataPosto,
          m.HoraPosto,
          m.UsuarioPosto,
          m.Encerrante,
          u.Usuario AS NomeUsuario
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        LEFT JOIN Usuarios u ON m.UsuarioPosto = u.Id
        WHERE m.Id = @movimentacaoId
      `;

      // Garantir que o valor do encerrante seja um número válido
      const encerranteNumerico = encerrante !== undefined && encerrante !== null ?
        parseFloat(encerrante) : 0;

      // Log para verificar o valor do encerrante antes de salvar
      request.log.info({
        message: 'Valor do encerrante a ser salvo',
        encerrante_original: encerrante,
        encerrante_numerico: encerranteNumerico
      });

      const result = await executeQuery(updateQuery, {
        movimentacaoId,
        litros,
        usuarioId,
        encerrante: encerranteNumerico
      });

      if (!result || !result[0]) {
        throw new Error('Falha ao registrar abastecimento');
      }

      // Log para verificar o valor do encerrante retornado do banco
      request.log.info({
        message: 'Valor do encerrante retornado do banco',
        encerrante_banco: result[0].Encerrante
      });

      return {
        message: 'Abastecimento registrado com sucesso',
        abastecimento: {
          id: result[0].Id,
          placa: result[0].Placa.trim(),
          descricaoVeiculo: result[0].DescricaoVeiculo ? result[0].DescricaoVeiculo.trim() : '',
          dataEntrada: result[0].DataEntrada,
          litragem: result[0].Litragem,
          dataAbastecimento: result[0].DataPosto,
          horaAbastecimento: result[0].HoraPosto,
          usuarioId: result[0].UsuarioPosto,
          encerrante: result[0].Encerrante,
          nomeUsuario: result[0].NomeUsuario ? result[0].NomeUsuario.trim() : ''
        }
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao registrar abastecimento'
      });
    }
  },

  /**
   * Lista histórico de abastecimentos de um veículo
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de abastecimentos
   */
  listarAbastecimentos: async (request, reply) => {
    const { placa } = request.params;

    try {
      // Buscar o ID do veículo na tabela Frota
      const veiculoQuery = `
        SELECT Id FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }

      const veiculoId = veiculos[0].Id;

      // Consultar registros de abastecimento na MovFrota
      const query = `
        SELECT
          m.Id,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          m.DataEntrada,
          m.HoraEntrada,
          m.Litragem,
          m.StatusPosto,
          m.DataPosto,
          m.HoraPosto,
          m.UsuarioPosto,
          m.Encerrante,
          u.Usuario AS NomeUsuario,
          c.Nome AS NomeMotorista
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        LEFT JOIN Usuarios u ON m.UsuarioPosto = u.Id
        LEFT JOIN Colaboradores c ON m.MotEntrada = c.Id
        WHERE m.Veiculo = @veiculoId
          AND (m.StatusPosto = '1' OR m.StatusPosto = 'OK')
          AND m.Litragem > 0
        ORDER BY m.DataPosto DESC, m.HoraPosto DESC
      `;

      const abastecimentos = await executeQuery(query, { veiculoId });

      // Processar os resultados para formatar campos
      const abastecimentosProcessados = abastecimentos.map(a => ({
        id: a.Id,
        placa: a.Placa.trim(),
        descricaoVeiculo: a.DescricaoVeiculo ? a.DescricaoVeiculo.trim() : '',
        dataEntrada: a.DataEntrada,
        horaEntrada: a.HoraEntrada,
        litragem: a.Litragem,
        dataAbastecimento: a.DataPosto,
        horaAbastecimento: a.HoraPosto,
        usuarioId: a.UsuarioPosto,
        encerrante: a.Encerrante,
        nomeUsuario: a.NomeUsuario ? a.NomeUsuario.trim() : '',
        nomeMotorista: a.NomeMotorista ? a.NomeMotorista.trim() : ''
      }));

      return {
        total: abastecimentosProcessados.length,
        abastecimentos: abastecimentosProcessados
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar abastecimentos'
      });
    }
  },

  /**
   * Lista veículos pendentes de abastecimento
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de veículos pendentes
   */
  listarPendentes: async (request, reply) => {
    try {
      const query = `
        SELECT
          m.Id,
          f.Id AS VeiculoId,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          f.Tipo AS TipoVeiculo,
          c.Nome AS NomeMotorista,
          c.Matricula AS MatriculaMotorista,
          m.DataEntrada,
          m.HoraEntrada,
          m.KmEntrada
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        INNER JOIN Colaboradores c ON m.MotEntrada = c.Id
        WHERE m.DataSaida IS NULL
          AND m.StatusPosto = '0'
          AND f.Tipo <> '4' -- Não listar carretas (tipo 4)
        ORDER BY m.DataEntrada ASC, m.HoraEntrada ASC
      `;

      const veiculos = await executeQuery(query);

      // Processar os resultados para formatar campos
      const veiculosProcessados = veiculos.map(v => ({
        id: v.Id,
        veiculoId: v.VeiculoId,
        placa: v.Placa.trim(),
        descricaoVeiculo: v.DescricaoVeiculo ? v.DescricaoVeiculo.trim() : '',
        tipoVeiculo: v.TipoVeiculo ? v.TipoVeiculo.trim() : '',
        nomeMotorista: v.NomeMotorista ? v.NomeMotorista.trim() : '',
        matriculaMotorista: v.MatriculaMotorista ? v.MatriculaMotorista.trim() : '',
        dataEntrada: v.DataEntrada,
        horaEntrada: v.HoraEntrada,
        kmEntrada: v.KmEntrada
      }));

      // Obter a data e hora atual do servidor SQL para cálculo correto do tempo no pátio
      // Converter para o fuso horário do Brasil (UTC-3)
      const dataHoraQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        SELECT DATEADD(HOUR, -3, GETUTCDATE()) AS dataHoraAtual
      `;
      const dataHoraResult = await executeQuery(dataHoraQuery);
      const dataHoraAtual = dataHoraResult[0].dataHoraAtual;

      return {
        total: veiculosProcessados.length,
        veiculos: veiculosProcessados,
        dataHoraAtual: dataHoraAtual
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar veículos pendentes de abastecimento'
      });
    }
  }
};

module.exports = abastecimentoController;