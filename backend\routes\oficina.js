const oficinaController = require('../controllers/oficinaController');

/**
 * <PERSON><PERSON>s de Oficina
 * @param {Object} fastify - Instância do Fastify
 * @param {Object} options - Opções do plugin
 */
async function oficinaRoutes(fastify, options) {
  // Schema para atualização de status
  const atualizarStatusSchema = {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'number' }
      }
    },
    body: {
      type: 'object',
      required: ['status'],
      properties: {
        status: {
          type: 'string',
          enum: ['1', '2', '3', '4', '5', '6', 'concluida']
        },
        observacao: { type: 'string' },
        colaborador_id: { type: 'number' }
      }
    }
  };

  // Schema para adicionar observação
  const adicionarObservacaoSchema = {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'number' }
      }
    },
    body: {
      type: 'object',
      required: ['observacao'],
      properties: {
        observacao: { type: 'string' }
      }
    }
  };

  // Schema para atualizar data prevista
  const atualizarDataPrevistaSchema = {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'number' }
      }
    },
    body: {
      type: 'object',
      properties: {
        dataPrevista: { type: 'string' },
        horaPrevista: { type: 'string' }
      }
    }
  };

  // Schema para atualizar tempo de serviço
  const atualizarTempoServicoSchema = {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'number' }
      }
    },
    body: {
      type: 'object',
      required: ['tempoServico'],
      properties: {
        tempoServico: { type: 'string' }
      }
    }
  };

  // Todas as rotas abaixo requerem autenticação
  fastify.register(async function (fastify) {
    // Aplicar middleware de autenticação a todas as rotas neste contexto
    fastify.addHook('preHandler', fastify.authenticate);

    // Rota para listar ordens de serviço
    fastify.get('/api/oficina/ordens', {
      schema: {
        querystring: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              enum: ['1', '2', '3', '4', '5', '6']
            }
          }
        }
      }
    }, oficinaController.listarOrdens);

    // Rota para buscar uma ordem específica
    fastify.get('/api/oficina/ordens/:id', {
      schema: {
        params: {
          type: 'object',
          required: ['id'],
          properties: {
            id: { type: 'number' }
          }
        }
      }
    }, oficinaController.buscarOrdem);

    // Rota para atualizar status de uma ordem
    fastify.put('/api/oficina/ordens/:id/status', { schema: atualizarStatusSchema }, oficinaController.atualizarStatus);

    // Rota para adicionar observação a uma ordem
    fastify.post('/api/oficina/ordens/:id/observacao', { schema: adicionarObservacaoSchema }, oficinaController.adicionarObservacao);

    // Rota para atualizar data prevista de conclusão
    fastify.put('/api/oficina/ordens/:id/data-prevista', { schema: atualizarDataPrevistaSchema }, oficinaController.atualizarDataPrevista);

    // Rota para atualizar tempo de serviço
    fastify.put('/api/oficina/ordens/:id/tempo-servico', { schema: atualizarTempoServicoSchema }, oficinaController.atualizarTempoServico);
  });
}

module.exports = oficinaRoutes;