# Scripts de Extração da Estrutura do Banco de Dados

Este diretório contém scripts para extrair e atualizar automaticamente a documentação da estrutura do banco de dados BWA Transportes.

## Scripts Disponíveis

### 1. `update-db-structure.ps1` (Recomendado)
**Uso:** Atualização automática do arquivo principal `db_structure.md`

```powershell
.\scripts\update-db-structure.ps1
```

**O que faz:**
- Conecta ao banco de dados SQL Server
- Extrai a estrutura atual (tabelas, colunas, índices)
- Cria backup do arquivo atual
- Substitui o arquivo `db_structure.md` com a estrutura atualizada
- Remove arquivos temporários

**Vantagens:**
- Processo totalmente automatizado
- Cria backup de segurança
- Mantém o arquivo principal sempre atualizado

### 2. `extract-db-structure.ps1`
**Uso:** Extração manual com opção de substituição

```powershell
.\scripts\extract-db-structure.ps1
```

**O que faz:**
- Verifica dependências e configurações
- Extrai estrutura do banco
- Gera arquivo `db_structure_extracted.md`
- Pergunta se deseja substituir o arquivo principal
- Opção de abrir o arquivo gerado

### 3. `get-db-structure.js`
**Uso:** Script Node.js para extração (usado pelos scripts PowerShell)

```bash
# A partir do diretório backend
node get-db-structure.js
```

**O que faz:**
- Script base que faz a conexão e extração
- Gera arquivo Markdown com estrutura completa
- Usado internamente pelos scripts PowerShell

## Pré-requisitos

### Software Necessário
- **Node.js** (versão 16 ou superior)
- **PowerShell** (Windows)
- **Acesso ao banco de dados** SQL Server

### Dependências Node.js
As dependências são instaladas automaticamente pelos scripts, mas incluem:
- `mssql` - Driver SQL Server
- `dotenv` - Carregamento de variáveis de ambiente

### Configuração do Banco
O arquivo `.env` deve existir em `backend/.env` com as configurações:

```env
DB_SERVER=**************
DB_PORT=1433
DB_NAME=BWA
DB_USER=sa
DB_PASSWORD=!bwa001
DB_ENCRYPT=true
DB_TRUST_SERVER_CERTIFICATE=true
DB_CONNECTION_TIMEOUT=30000
DB_REQUEST_TIMEOUT=30000
```

## Como Usar

### Atualização Rápida (Recomendado)
```powershell
# Execute a partir do diretório raiz do projeto
.\scripts\update-db-structure.ps1
```

### Primeira Configuração
```powershell
# Se for a primeira vez, use o script completo
.\scripts\extract-db-structure.ps1
```

### Verificação Manual
```powershell
# Para verificar se tudo está funcionando
cd backend
node get-db-structure.js
```

## Estrutura dos Arquivos Gerados

### Formato do Arquivo Markdown
```markdown
# Estrutura do Banco de Dados - BWA Transportes

Extraído em: [data/hora]

## Tabelas

### [NomeTabela]

| Coluna | Tipo | Nulo | Identity | PK |
|--------|------|------|----------|----| 
| ...    | ...  | ...  | ...      | ...|

#### Índices

| Nome | Tipo | Único | PK | Colunas |
|------|------|-------|----|---------|
| ...  | ...  | ...   | ..| ...     |
```

### Informações Extraídas
- **Tabelas:** Nome e schema
- **Colunas:** Nome, tipo de dados, nullable, identity, primary key
- **Índices:** Nome, tipo, unique, primary key, colunas
- **Timestamp:** Data e hora da extração

## Solução de Problemas

### Erro: "Node.js não encontrado"
```bash
# Instale o Node.js
https://nodejs.org/
```

### Erro: "Arquivo .env não encontrado"
```bash
# Execute o script de configuração primeiro
.\scripts\extract-db-structure.ps1
```

### Erro: "Cannot find module 'mssql'"
```bash
# Instale as dependências manualmente
cd backend
npm install
```

### Erro de Conexão com Banco
- Verifique se o servidor está acessível
- Confirme as credenciais no arquivo `.env`
- Teste a conectividade de rede

## Automação

### Agendamento (Opcional)
Para manter a documentação sempre atualizada, você pode agendar a execução:

```powershell
# Criar tarefa agendada (exemplo)
schtasks /create /tn "BWA-UpdateDB" /tr "powershell -File C:\caminho\scripts\update-db-structure.ps1" /sc daily /st 02:00
```

### Integração com Git
```bash
# Adicionar ao .gitignore se necessário
echo "db_structure_backup_*.md" >> .gitignore
```

## Arquivos de Backup

Os scripts criam backups automáticos com formato:
- `db_structure_backup_YYYYMMDD_HHMMSS.md`

Estes arquivos podem ser removidos periodicamente para economizar espaço.

## Suporte

Para problemas ou melhorias nos scripts, consulte:
- Documentação do projeto
- Logs de erro dos scripts
- Configurações de rede e banco de dados
