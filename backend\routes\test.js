const { executeQuery } = require('../models/db');

/**
 * Rotas de teste para verificar a conexão com o banco de dados
 * @param {FastifyInstance} fastify Instância do Fastify
 * @param {Object} options Opções do plugin
 */
async function testRoutes(fastify, options) {
  // Rota para testar a conexão com o banco de dados
  fastify.get('/api/test/db', async (request, reply) => {
    try {
      // Consulta simples para verificar a conexão
      const query = 'SELECT TOP 5 * FROM Usuarios';
      const result = await executeQuery(query);

      return {
        success: true,
        message: 'Conexão com o banco de dados estabelecida com sucesso',
        data: result
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        success: false,
        message: 'Erro ao conectar com o banco de dados',
        error: error.message
      });
    }
  });

  // Rota para testar a conexão com o banco de dados (tabelas)
  fastify.get('/api/test/tables', async (request, reply) => {
    try {
      // Consulta para listar as tabelas do banco de dados
      const query = `
        SELECT
          t.name AS TableName,
          s.name AS SchemaName
        FROM
          sys.tables t
        INNER JOIN
          sys.schemas s ON t.schema_id = s.schema_id
        ORDER BY
          s.name, t.name
      `;

      const result = await executeQuery(query);

      return {
        success: true,
        message: 'Lista de tabelas obtida com sucesso',
        data: result
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        success: false,
        message: 'Erro ao listar tabelas do banco de dados',
        error: error.message
      });
    }
  });

  // Rota para listar os veículos da frota
  fastify.get('/api/test/frota', async (request, reply) => {
    try {
      // Primeiro vamos verificar as colunas da tabela Frota
      const schemaQuery = `
        SELECT
          COLUMN_NAME
        FROM
          INFORMATION_SCHEMA.COLUMNS
        WHERE
          TABLE_NAME = 'Frota'
        ORDER BY
          ORDINAL_POSITION
      `;

      const columns = await executeQuery(schemaQuery);
      fastify.log.info({ columns }, 'Colunas da tabela Frota');

      // Consulta para listar os veículos da frota
      const query = `
        SELECT TOP 10
          Id,
          Placa,
          Descricao,
          Tipo,
          Ano,
          Aquisicao
        FROM
          Frota
        ORDER BY
          Id
      `;

      const result = await executeQuery(query);

      return {
        success: true,
        message: 'Lista de veículos obtida com sucesso',
        data: result
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        success: false,
        message: 'Erro ao listar veículos da frota',
        error: error.message
      });
    }
  });

  // Rota para listar os colaboradores
  fastify.get('/api/test/colaboradores', async (request, reply) => {
    try {
      // Primeiro vamos verificar as colunas da tabela Colaboradores
      const schemaQuery = `
        SELECT
          COLUMN_NAME
        FROM
          INFORMATION_SCHEMA.COLUMNS
        WHERE
          TABLE_NAME = 'Colaboradores'
        ORDER BY
          ORDINAL_POSITION
      `;

      const columns = await executeQuery(schemaQuery);
      fastify.log.info({ columns }, 'Colunas da tabela Colaboradores');

      // Consulta para listar os colaboradores
      const query = `
        SELECT TOP 10
          Id,
          Matricula,
          Nome,
          Funcao,
          Local
        FROM
          Colaboradores
        ORDER BY
          Nome
      `;

      const result = await executeQuery(query);

      return {
        success: true,
        message: 'Lista de colaboradores obtida com sucesso',
        data: result
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        success: false,
        message: 'Erro ao listar colaboradores',
        error: error.message
      });
    }
  });

  // Rota para listar os usuários
  fastify.get('/api/test/usuarios', async (request, reply) => {
    try {
      // Primeiro vamos verificar as colunas da tabela Usuarios
      const schemaQuery = `
        SELECT
          COLUMN_NAME
        FROM
          INFORMATION_SCHEMA.COLUMNS
        WHERE
          TABLE_NAME = 'Usuarios'
        ORDER BY
          ORDINAL_POSITION
      `;

      const columns = await executeQuery(schemaQuery);
      fastify.log.info({ columns }, 'Colunas da tabela Usuarios');

      // Consulta para listar os usuários
      const query = `
        SELECT TOP 10
          Id,
          Usuario,
          Tipo,
          Email,
          Colaborador
        FROM
          Usuarios
        ORDER BY
          Usuario
      `;

      const result = await executeQuery(query);

      return {
        success: true,
        message: 'Lista de usuários obtida com sucesso',
        data: result
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        success: false,
        message: 'Erro ao listar usuários',
        error: error.message
      });
    }
  });
}

module.exports = testRoutes;
