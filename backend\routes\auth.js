const authController = require('../controllers/authController');

/**
 * Rotas de autenticação
 * @param {Object} fastify - Instância do Fastify
 * @param {Object} options - Opções do plugin
 */
async function authRoutes(fastify, options) {
  // Schema para validação do login
  const loginSchema = {
    body: {
      type: 'object',
      required: ['username', 'password'],
      properties: {
        username: { type: 'string' },
        password: { type: 'string' }
      }
    },
    response: {
      200: {
        type: 'object',
        properties: {
          token: { type: 'string' },
          user: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              nome: { type: 'string' },
              tipo: { type: 'string' },
              email: { type: 'string' }
            }
          }
        }
      },
      401: {
        type: 'object',
        properties: {
          error: { type: 'string' }
        }
      }
    }
  };

  // Rota de login
  fastify.post('/api/auth/login', { schema: loginSchema }, authController.login);

  // Rota para verificar token (protegida)
  fastify.get('/api/auth/verify', {
    preHandler: fastify.authenticate,
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            user: { type: 'object' }
          }
        }
      }
    }
  }, authController.verify);
}

module.exports = authRoutes;