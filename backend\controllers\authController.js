const { executeQuery } = require('../models/db');

/**
 * Controlador de autenticação
 * Gerencia login e verificação de usuários
 */
const authController = {
  /**
   * Login de usuário
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Objeto com token JWT e informações do usuário
   */
  login: async (request, reply) => {
    const { username, password } = request.body;

    try {
      // Consulta SQL para verificar usuário e senha
      // Usando RTRIM para remover espaços em branco dos campos nchar
      const query = `
        SELECT u.Id, u.Usuario, u.Tipo, u.Email, u.Colaborador,
               c.Nome as NomeColaborador
        FROM Usuarios u
        LEFT JOIN Colaboradores c ON u.Colaborador = c.Id
        WHERE RTRIM(u.Usuario) = @username AND RTRIM(u.Senha) = @password
      `;

      const users = await executeQuery(query, {
        username,
        password // Em produção, usar hash de senha
      });

      if (users.length === 0) {
        return reply.code(401).send({
          error: 'Credenciais inválidas'
        });
      }

      const user = users[0];

      // Gerar token JWT
      const token = request.server.jwt.sign({
        id: user.Id,
        username: user.Usuario.trim(),
        tipo: user.Tipo.trim()
      }, {
        expiresIn: '8h'
      });

      return {
        token,
        user: {
          id: user.Id,
          nome: user.NomeColaborador ? user.NomeColaborador.trim() : user.Usuario.trim(),
          tipo: user.Tipo.trim(),
          email: user.Email ? user.Email.trim() : null
        }
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao realizar login'
      });
    }
  },

  /**
   * Verifica se o token é válido
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Informações do usuário
   */
  verify: async (request, reply) => {
    try {
      // O middleware de autenticação já verificou o token
      // Podemos obter informações atualizadas do usuário, se necessário
      if (request.user && request.user.id) {
        const query = `
          SELECT u.Id, u.Usuario, u.Tipo, u.Email, u.Colaborador,
                 c.Nome as NomeColaborador
          FROM Usuarios u
          LEFT JOIN Colaboradores c ON u.Colaborador = c.Id
          WHERE u.Id = @id
        `;
        
        const users = await executeQuery(query, { id: request.user.id });
        
        if (users.length > 0) {
          const user = users[0];
          return {
            user: {
              id: user.Id,
              nome: user.NomeColaborador ? user.NomeColaborador.trim() : user.Usuario.trim(),
              tipo: user.Tipo.trim(),
              email: user.Email ? user.Email.trim() : null
            }
          };
        }
      }
      
      return {
        user: request.user
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao verificar token'
      });
    }
  }
};

module.exports = authController;