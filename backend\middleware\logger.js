/**
 * Middleware de logging
 * Registra informações sobre as requisições e respostas
 */
const fp = require('fastify-plugin');

const logger = async (fastify) => {
  fastify.addHook('onRequest', async (request, reply) => {
    request.log.info({
      url: request.url,
      method: request.method,
      ip: request.ip,
      userAgent: request.headers['user-agent']
    }, 'Requisição recebida');
  });

  fastify.addHook('onResponse', async (request, reply) => {
    request.log.info({
      url: request.url,
      method: request.method,
      statusCode: reply.statusCode,
      responseTime: reply.getResponseTime()
    }, 'Resposta enviada');
  });

  fastify.addHook('onError', async (request, reply, error) => {
    request.log.error({
      url: request.url,
      method: request.method,
      error: {
        message: error.message,
        stack: error.stack
      }
    }, 'Erro na requisição');
  });
};

module.exports = fp(logger);