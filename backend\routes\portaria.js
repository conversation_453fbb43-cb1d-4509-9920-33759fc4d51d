const portariaController = require('../controllers/portariaController');

/**
 * <PERSON><PERSON><PERSON> da Portaria
 * @param {Object} fastify - Instância do Fastify
 * @param {Object} options - Opções do plugin
 */
async function portariaRoutes(fastify, options) {
  // Schema para validação de entrada de veículo
  const entradaSchema = {
    body: {
      type: 'object',
      required: ['placa', 'matricula_motorista', 'bafometro', 'quilometragem'],
      properties: {
        placa: { type: 'string' },
        matricula_motorista: { type: 'string' },
        bafometro: { type: 'number', minimum: 0, maximum: 1 },
        quilometragem: { type: 'number' },
        placa_carreta: { type: 'string' }
      }
    }
  };

  // Schema para validação de saída de veículo
  const saidaSchema = {
    body: {
      type: 'object',
      required: ['placa', 'bafometro', 'quilometragem', 'matricula_motorista_saida'],
      properties: {
        placa: { type: 'string' },
        bafometro: { type: 'number', minimum: 0, maximum: 1 },
        quilometragem: { type: 'number' },
        matricula_motorista_saida: { type: 'string' },
        placa_carreta: { type: 'string' }
      }
    }
  };

  // Todas as rotas abaixo requerem autenticação
  fastify.register(async function (fastify) {
    // Aplicar middleware de autenticação a todas as rotas neste contexto
    fastify.addHook('preHandler', fastify.authenticate);

    // Rota para registrar entrada de veículo
    fastify.post('/api/portaria/entrada', { schema: entradaSchema }, portariaController.registrarEntrada);

    // Rota para registrar saída de veículo
    fastify.post('/api/portaria/saida', { schema: saidaSchema }, portariaController.registrarSaida);

    // Rota para listar veículos no pátio
    fastify.get('/api/portaria/veiculos', portariaController.listarVeiculosNoPatio);

    // Rota para consultar veículo por placa
    fastify.get('/api/portaria/veiculo/:placa', {
      schema: {
        params: {
          type: 'object',
          required: ['placa'],
          properties: {
            placa: { type: 'string' }
          }
        }
      }
    }, portariaController.consultarVeiculo);

    // Rota para verificar liberação de saída de veículo
    fastify.get('/api/portaria/verificar-liberacao/:id', {
      schema: {
        params: {
          type: 'object',
          required: ['id'],
          properties: {
            id: { type: 'string' }
          }
        }
      }
    }, portariaController.verificarLiberacao);
  });
}

module.exports = portariaRoutes;