const { executeQuery } = require('../models/db');

/**
 * Controlador de Colaboradores
 * Gerencia informações de colaboradores/motoristas
 */
const colaboradoresController = {
  /**
   * Lista colaboradores (motoristas)
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de colaboradores
   */
  listarMotoristas: async (request, reply) => {
    try {
      const query = `
        SELECT
          Id,
          RTRIM(Matricula) AS Matricula,
          RTRIM(Nome) AS Nome,
          RTRIM(Funcao) AS Funcao
        FROM Colaboradores
        WHERE RTRIM(Funcao) LIKE '%MOTORISTA%'
        ORDER BY Nome
      `;

      const motoristas = await executeQuery(query);

      return {
        total: motoristas.length,
        motoristas: motoristas
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar motoristas'
      });
    }
  },

  /**
   * Busca um colaborador pela matrícula
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados do colaborador
   */
  buscarPorMatricula: async (request, reply) => {
    const { matricula } = request.params;

    try {
      const query = `
        SELECT
          Id,
          RTRIM(Matricula) AS Matricula,
          RTRIM(Nome) AS Nome,
          RTRIM(Funcao) AS Funcao
        FROM Colaboradores
        WHERE RTRIM(Matricula) = @matricula
      `;

      const colaboradores = await executeQuery(query, { matricula });

      if (colaboradores.length === 0) {
        return reply.code(404).send({
          error: 'Colaborador não encontrado'
        });
      }

      return {
        colaborador: colaboradores[0]
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao buscar colaborador'
      });
    }
  }
};

module.exports = colaboradoresController;
