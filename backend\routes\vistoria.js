const vistoriaController = require('../controllers/vistoriaController');

/**
 * <PERSON><PERSON>s de Vistoria
 * @param {Object} fastify - Instância do Fastify
 * @param {Object} options - Opções do plugin
 */
async function vistoriaRoutes(fastify, options) {
  // Schema para validação de registro de vistoria
  const vistoriaSchema = {
    body: {
      type: 'object',
      required: ['placa', 'itens_checklist'],
      properties: {
        placa: { type: 'string' },
        itens_checklist: { 
          type: 'array',
          items: {
            type: 'object',
            required: ['id', 'descricao', 'status'],
            properties: {
              id: { type: 'number' },
              descricao: { type: 'string' },
              status: { type: 'boolean' },
              observacao: { type: 'string' }
            }
          }
        },
        observacao: { type: 'string' }
      }
    }
  };

  // Todas as rotas abaixo requerem autenticação
  fastify.register(async function (fastify) {
    // Aplicar middleware de autenticação a todas as rotas neste contexto
    fastify.addHook('preHandler', fastify.authenticate);

    // Rota para registrar vistoria
    fastify.post('/api/vistoria', { schema: vistoriaSchema }, vistoriaController.registrarVistoria);

    // Rota para listar itens do checklist por tipo (opcional)
    fastify.get('/api/vistoria/checklist/:tipo?', {
      schema: {
        params: {
          type: 'object',
          properties: {
            tipo: { type: 'string' }
          }
        }
      }
    }, vistoriaController.listarItensChecklist);

    // Rota para obter checklist específico para um veículo pela placa
    fastify.get('/api/vistoria/checklist/veiculo/:placa', {
      schema: {
        params: {
          type: 'object',
          required: ['placa'],
          properties: {
            placa: { type: 'string' }
          }
        }
      }
    }, vistoriaController.obterChecklistPorPlaca);

    // Rota para listar veículos pendentes de vistoria
    fastify.get('/api/vistoria/pendentes', vistoriaController.listarPendentes);
  });
}

module.exports = vistoriaRoutes;