const { executeQuery } = require('../models/db');

/**
 * Controlador de Vistoria
 * Gerencia checklist e vistorias de veículos
 */
const vistoriaController = {
  /**
   * Registra vistoria de veículo
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados da vistoria registrada
   */
  registrarVistoria: async (request, reply) => {
    const {
      placa,
      itens_checklist,
      observacao
    } = request.body;

    try {
      // Buscar o ID do veículo e seu tipo na tabela Frota
      const veiculoQuery = `
        SELECT Id, Tipo FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }

      const veiculoId = veiculos[0].Id;
      const tipoVeiculo = veiculos[0].Tipo ? veiculos[0].Tipo.trim().charAt(0) : '';

      // Verificar se o veículo está no pátio
      const veiculoPatioQuery = `
        SELECT TOP 1 Id FROM MovFrota
        WHERE Veiculo = @veiculoId
        AND DataSaida IS NULL
        ORDER BY DataEntrada DESC
      `;

      const veiculosPatio = await executeQuery(veiculoPatioQuery, { veiculoId });

      if (veiculosPatio.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no pátio'
        });
      }

      const movimentacaoId = veiculosPatio[0].Id;

      // Verificar se algum item do checklist está com problema
      const itensComProblema = itens_checklist.filter(item => !item.status);
      const necessitaManutencao = itensComProblema.length > 0;

      // Obter o ID do usuário atual a partir do token JWT
      const usuarioId = request.user.id;

      // Atualizar registro na MovFrota com dados de vistoria
      // Sempre marcar StatusVist como '1' (OK) independente do resultado dos itens
      // Usar o fuso horário do Brasil (UTC-3)
      const updateQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
        DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
        DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

        UPDATE MovFrota SET
          StatusVist = '1', -- Sempre marcar como válido, independente do resultado
          UsuarioVist = @usuarioId,
          DataVist = @dataHoraBrasil,
          HoraVist = @horaFormatada
        WHERE Id = @movimentacaoId;

        SELECT
          m.Id,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          f.Tipo AS TipoVeiculo,
          m.DataEntrada,
          m.StatusVist,
          m.DataVist,
          m.HoraVist,
          m.UsuarioVist,
          u.Usuario AS NomeUsuario
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        LEFT JOIN Usuarios u ON m.UsuarioVist = u.Id
        WHERE m.Id = @movimentacaoId
      `;

      const result = await executeQuery(updateQuery, {
        movimentacaoId,
        usuarioId
      });

      if (!result || !result[0]) {
        throw new Error('Falha ao registrar vistoria');
      }

      // Se necessita manutenção, criar uma ordem de serviço para cada item reprovado
      if (necessitaManutencao) {
        request.log.info(`Criando OS para veículo com placa ${placa} - Itens com problema: ${itensComProblema.length}`);

        // Obter o ID do veículo para associar à OS
        const veiculoQuery = `
          SELECT Id FROM Frota
          WHERE RTRIM(Placa) = @placa
        `;

        const veiculoResult = await executeQuery(veiculoQuery, { placa });
        const veiculoId = veiculoResult[0]?.Id;

        // Array para armazenar os IDs das OS criadas
        const osIds = [];

        // Criar uma OS para cada item reprovado
        for (const item of itensComProblema) {
          // Usar a observação específica do item se disponível, caso contrário usar a observação geral
          const itemObservacao = item.observacao || observacao || '';

          const criarOSQuery = `
            -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
            DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
            DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
            DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

            INSERT INTO OS (
              Descricao,
              Abertura,
              AberHora,
              Status,
              Observacoes,
              Usuario,
              UsuarioData,
              UsuarioHora,
              VeiculoFrota,
              MovFrota,
              ItemChecklist
            ) VALUES (
              @descricao,
              @dataHoraBrasil,
              @horaFormatada,
              '1', -- 1 = Aguardando aceite
              '', -- Deixar observações vazio para uso posterior pela oficina
              @usuarioId,
              @dataHoraBrasil,
              @horaFormatada,
              @veiculoId,
              @movimentacaoId,
              @itemId
            );

            SELECT SCOPE_IDENTITY() as id;
          `;

          const osResult = await executeQuery(criarOSQuery, {
            descricao: itemObservacao.substring(0, 120), // Usar a observação do vistoriador como descrição
            usuarioId,
            veiculoId,
            movimentacaoId,
            itemId: item.id
          });

          if (osResult && osResult[0]) {
            const osId = osResult[0].id;
            osIds.push(osId);
            request.log.info(`OS criada com sucesso para item ${item.id}. ID da OS: ${osId}`);
          }
        }

        if (osIds.length > 0) {
          return {
            message: `Vistoria registrada com sucesso. ${osIds.length} OS criadas para os itens com problema.`,
            vistoria: {
              id: result[0].Id,
              placa: result[0].Placa.trim(),
              descricaoVeiculo: result[0].DescricaoVeiculo ? result[0].DescricaoVeiculo.trim() : '',
              tipoVeiculo: result[0].TipoVeiculo ? result[0].TipoVeiculo.trim() : '',
              dataVistoria: result[0].DataVist,
              horaVistoria: result[0].HoraVist,
              usuarioId: result[0].UsuarioVist,
              nomeUsuario: result[0].NomeUsuario ? result[0].NomeUsuario.trim() : '',
              status: 'Reprovado (OS criadas)'
            },
            necessitaManutencao: true,
            osIds: osIds,
            itensComProblema
          };
        }
      }

      return {
        message: 'Vistoria registrada com sucesso',
        vistoria: {
          id: result[0].Id,
          placa: result[0].Placa.trim(),
          descricaoVeiculo: result[0].DescricaoVeiculo ? result[0].DescricaoVeiculo.trim() : '',
          tipoVeiculo: result[0].TipoVeiculo ? result[0].TipoVeiculo.trim() : '',
          dataVistoria: result[0].DataVist,
          horaVistoria: result[0].HoraVist,
          usuarioId: result[0].UsuarioVist,
          nomeUsuario: result[0].NomeUsuario ? result[0].NomeUsuario.trim() : '',
          status: 'Aprovado'
        },
        necessitaManutencao: false
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao registrar vistoria'
      });
    }
  },

  /**
   * Lista itens do checklist para um tipo específico de veículo
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de itens do checklist
   */
  listarItensChecklist: async (request, reply) => {
    const { tipo } = request.params;

    try {
      let tipoQuery = '';
      let params = {};

      // Se o tipo for informado, filtrar por ele
      if (tipo) {
        tipoQuery = 'WHERE RTRIM(Tipo) = @tipo';
        params = { tipo };
      }

      const query = `
        SELECT
          Id,
          Tipo,
          Descricao
        FROM CheckList
        ${tipoQuery}
        ORDER BY Id
      `;

      const itens = await executeQuery(query, params);

      // Formatar os itens
      const itensFormatados = itens.map(item => ({
        id: item.Id,
        tipo: item.Tipo ? item.Tipo.trim() : '',
        descricao: item.Descricao ? item.Descricao.trim() : ''
      }));

      return {
        total: itensFormatados.length,
        itens: itensFormatados
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar itens do checklist'
      });
    }
  },

  /**
   * Obtém o checklist específico para um veículo pela placa
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de itens do checklist para o tipo de veículo
   */
  obterChecklistPorPlaca: async (request, reply) => {
    const { placa } = request.params;

    try {
      // Buscar o tipo do veículo pelo código do veículo
      const veiculoQuery = `
        SELECT Id, Tipo FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }

      const tipoVeiculo = veiculos[0].Tipo ? veiculos[0].Tipo.trim().charAt(0) : '';

      // Consultar os itens do checklist para esse tipo de veículo
      const checklistQuery = `
        SELECT
          Id,
          Tipo,
          Descricao
        FROM CheckList
        WHERE RTRIM(Tipo) = @tipoVeiculo
        ORDER BY Id
      `;

      const itens = await executeQuery(checklistQuery, { tipoVeiculo });

      // Formatar os itens
      const itensFormatados = itens.map(item => ({
        id: item.Id,
        tipo: item.Tipo ? item.Tipo.trim() : '',
        descricao: item.Descricao ? item.Descricao.trim() : ''
      }));

      return {
        placa,
        tipoVeiculo,
        total: itensFormatados.length,
        itens: itensFormatados
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao obter checklist por placa'
      });
    }
  },

  /**
   * Lista veículos pendentes de vistoria
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de veículos pendentes
   */
  listarPendentes: async (request, reply) => {
    try {
      const query = `
        SELECT
          m.Id,
          f.Id AS VeiculoId,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          f.Tipo AS TipoVeiculo,
          c.Nome AS NomeMotorista,
          c.Matricula AS MatriculaMotorista,
          m.DataEntrada,
          m.HoraEntrada,
          m.KmEntrada
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        INNER JOIN Colaboradores c ON m.MotEntrada = c.Id
        WHERE m.DataSaida IS NULL
          AND m.StatusVist = '0'
        ORDER BY m.DataEntrada ASC, m.HoraEntrada ASC
      `;

      const veiculos = await executeQuery(query);

      // Processar os resultados para formatar campos
      const veiculosProcessados = veiculos.map(v => ({
        id: v.Id,
        veiculoId: v.VeiculoId,
        placa: v.Placa.trim(),
        descricaoVeiculo: v.DescricaoVeiculo ? v.DescricaoVeiculo.trim() : '',
        tipoVeiculo: v.TipoVeiculo ? v.TipoVeiculo.trim() : '',
        nomeMotorista: v.NomeMotorista ? v.NomeMotorista.trim() : '',
        matriculaMotorista: v.MatriculaMotorista ? v.MatriculaMotorista.trim() : '',
        dataEntrada: v.DataEntrada,
        horaEntrada: v.HoraEntrada,
        kmEntrada: v.KmEntrada
      }));

      // Obter a data e hora atual do servidor SQL para cálculo correto do tempo no pátio
      // Converter para o fuso horário do Brasil (UTC-3)
      const dataHoraQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        SELECT DATEADD(HOUR, -3, GETUTCDATE()) AS dataHoraAtual
      `;
      const dataHoraResult = await executeQuery(dataHoraQuery);
      const dataHoraAtual = dataHoraResult[0].dataHoraAtual;

      return {
        total: veiculosProcessados.length,
        veiculos: veiculosProcessados,
        dataHoraAtual: dataHoraAtual
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar veículos pendentes de vistoria'
      });
    }
  }
};

module.exports = vistoriaController;