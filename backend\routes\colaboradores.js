const colaboradoresController = require('../controllers/colaboradoresController');

/**
 * Rotas de Colaboradores
 * @param {Object} fastify - Instância do Fastify
 * @param {Object} options - Opções do plugin
 */
async function colaboradoresRoutes(fastify, options) {
  // Todas as rotas abaixo requerem autenticação
  fastify.register(async function (fastify) {
    // Aplicar middleware de autenticação a todas as rotas neste contexto
    fastify.addHook('preHandler', fastify.authenticate);

    // Rota para listar motoristas
    fastify.get('/api/colaboradores/motoristas', colaboradoresController.listarMotoristas);

    // Rota para buscar colaborador por matrícula
    fastify.get('/api/colaboradores/matricula/:matricula', {
      schema: {
        params: {
          type: 'object',
          required: ['matricula'],
          properties: {
            matricula: { type: 'string' }
          }
        }
      }
    }, colaboradoresController.buscarPorMatricula);
  });
}

module.exports = colaboradoresRoutes;
