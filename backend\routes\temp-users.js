const { executeQuery } = require('../models/db');

/**
 * Rota temporária para listar usuários
 * @param {Object} fastify - Instância do Fastify
 * @param {Object} options - Opções do plugin
 */
async function tempUsersRoutes(fastify, options) {
  // Rota para listar todos os usuários
  fastify.get('/api/temp/users', async (request, reply) => {
    try {
      // Consulta SQL para listar todos os usuários com seus tipos
      const query = `
        SELECT TOP 100 u.Id, RTRIM(u.Usuario) AS Usuario, RTRIM(u.Senha) AS Senha,
               RTRIM(u.Tipo) AS Tipo, c.Nome as NomeColaborador
        FROM Usuarios u
        LEFT JOIN Colaboradores c ON u.Colaborador = c.Id
        ORDER BY u.Tipo, u.Id
      `;

      const users = await executeQuery(query);

      return {
        success: true,
        users: users.map(user => ({
          id: user.Id,
          usuario: user.Usuario,
          senha: user.<PERSON>ha,
          tipo: user.Tipo,
          nome: user.NomeColaborador ? user.NomeColaborador.trim() : null
        }))
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: 'Erro ao listar usuários',
        error: error.message
      });
    }
  });


}

module.exports = tempUsersRoutes;
