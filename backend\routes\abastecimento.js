const abastecimentoController = require('../controllers/abastecimentoController');

/**
 * <PERSON><PERSON>s de Abastecimento
 * @param {Object} fastify - Instância do Fastify
 * @param {Object} options - Opções do plugin
 */
async function abastecimentoRoutes(fastify, options) {
  // Schema para validação de registro de abastecimento
  const abastecimentoSchema = {
    body: {
      type: 'object',
      required: ['placa', 'litros'],
      properties: {
        placa: { type: 'string' },
        litros: { type: 'number', minimum: 0 },
        encerrante: { type: 'number', minimum: 0 },
        observacao: { type: 'string' }
      }
    }
  };

  // Todas as rotas abaixo requerem autenticação
  fastify.register(async function (fastify) {
    // Aplicar middleware de autenticação a todas as rotas neste contexto
    fastify.addHook('preHandler', fastify.authenticate);

    // Rota para registrar abastecimento
    fastify.post('/api/abastecimento', { schema: abastecimentoSchema }, abastecimentoController.registrarAbastecimento);

    // Rota para listar abastecimentos de um veículo
    fastify.get('/api/abastecimento/veiculo/:placa', {
      schema: {
        params: {
          type: 'object',
          required: ['placa'],
          properties: {
            placa: { type: 'string' }
          }
        }
      }
    }, abastecimentoController.listarAbastecimentos);

    // Rota para listar veículos pendentes de abastecimento
    fastify.get('/api/abastecimento/pendentes', abastecimentoController.listarPendentes);
  });
}

module.exports = abastecimentoRoutes;