const { executeQuery } = require('../models/db');
const { isStatusOk, processarVeiculo, processarVeiculos } = require('../utils/statusUtils');

/**
 * Controlador da Portaria
 * Gerencia entrada e saída de veículos
 */
const portariaController = {
  /**
   * Registra entrada de veículo
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados do registro criado
   */
  registrarEntrada: async (request, reply) => {
    const {
      placa,
      matricula_motorista,
      bafometro,
      quilometragem,
      placa_carreta
    } = request.body;

    try {
      // Buscar o ID e informações do veículo principal na tabela Frota
      const veiculoQuery = `
        SELECT Id, Tipo FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }

      const veiculoId = veiculos[0].Id;
      const tipoVeiculo = veiculos[0].Tipo ? veiculos[0].Tipo.trim() : '';

      // Identificar o tipo de veículo com base no campo Tipo
      // Tipo 1: Cavalo, Tipo 3: Cavalo Truck, Tipo 4: Carreta
      const tipoVeiculoNumerico = parseInt(tipoVeiculo);
      const isCavalo = tipoVeiculoNumerico === 1;
      const isCavaloTruck = tipoVeiculoNumerico === 3;
      const isCarreta = tipoVeiculoNumerico === 4;

      // Para compatibilidade com código existente, manter a verificação de 'L'
      const isCavaloMecanico = tipoVeiculo === 'L' || isCavalo;

      // Validação específica para cavalo mecânico
      if (isCavaloMecanico && !placa_carreta) {
        // Alerta, mas não impede o cadastro
        request.log.warn('Cavalo mecânico registrado sem informar a placa da carreta');
        // Não retornamos erro, permitindo que o cadastro continue
      }

      // Verificar se o veículo já está no pátio (sem registro de saída)
      const veiculoPatioQuery = `
        SELECT TOP 1 Id FROM MovFrota
        WHERE Veiculo = @veiculoId
        AND DataSaida IS NULL
        ORDER BY DataEntrada DESC
      `;

      const veiculoExistente = await executeQuery(veiculoPatioQuery, { veiculoId });

      if (veiculoExistente.length > 0) {
        return reply.code(400).send({
          error: 'Veículo já possui entrada registrada sem saída correspondente'
        });
      }

      // Buscar ID do motorista
      const motoristaQuery = `
        SELECT Id FROM Colaboradores
        WHERE RTRIM(Matricula) = @matricula_motorista
      `;

      const motoristas = await executeQuery(motoristaQuery, { matricula_motorista });

      if (motoristas.length === 0) {
        return reply.code(404).send({
          error: 'Motorista não encontrado no cadastro'
        });
      }

      const motoristaId = motoristas[0].Id;

      // Obter o ID do usuário atual a partir do token JWT
      const usuarioId = request.user.id;

      // Inserir registro de entrada para o veículo principal (cavalo mecânico ou outro)
      // Usar o SQL Server para definir tanto a data quanto a hora, garantindo consistência
      // Converter para o fuso horário do Brasil (UTC-3)
      const insertQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
        DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
        DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

        INSERT INTO MovFrota (
          Veiculo,
          DataEntrada,
          HoraEntrada,
          BafometroEnt,
          MotEntrada,
          KmEntrada,
          UsuarioPort,
          StatusPosto,
          StatusLav,
          StatusVist
        ) VALUES (
          @veiculoId,
          @dataHoraBrasil,
          @horaFormatada,
          @bafometro,
          @motoristaId,
          @quilometragem,
          @usuarioId,
          '0', '0', '0'
        );

        SELECT SCOPE_IDENTITY() as id;
      `;

      const result = await executeQuery(insertQuery, {
        veiculoId,
        bafometro,
        motoristaId,
        quilometragem,
        usuarioId
      });

      if (!result || !result[0]) {
        throw new Error('Falha ao inserir registro para o veículo principal');
      }

      const registroPrincipalId = result[0].id;
      let registroCarretaId = null;

      // Se for cavalo (tipo 1) ou cavalo mecânico (tipo 'L') com carreta, registrar a entrada da carreta
      if ((isCavalo || tipoVeiculo === 'L') && placa_carreta) {
        // Buscar o ID da carreta na tabela Frota
        const carretaQuery = `
          SELECT Id FROM Frota
          WHERE RTRIM(Placa) = @placa_carreta
        `;

        const carretas = await executeQuery(carretaQuery, { placa_carreta });

        if (carretas.length === 0) {
          return reply.code(404).send({
            error: 'Carreta não encontrada no cadastro'
          });
        }

        const carretaId = carretas[0].Id;

        // Verificar se a carreta já está no pátio
        const carretaPatioQuery = `
          SELECT TOP 1 Id FROM MovFrota
          WHERE Veiculo = @carretaId
          AND DataSaida IS NULL
          ORDER BY DataEntrada DESC
        `;

        const carretaExistente = await executeQuery(carretaPatioQuery, { carretaId });

        if (carretaExistente.length > 0) {
          return reply.code(400).send({
            error: 'Carreta já possui entrada registrada sem saída correspondente'
          });
        }

        // Inserir registro para a carreta (sem vinculação permanente ao cavalo mecânico)
        const insertCarretaQuery = `
          -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
          DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
          DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
          DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

          INSERT INTO MovFrota (
            Veiculo,
            DataEntrada,
            HoraEntrada,
            BafometroEnt,
            MotEntrada,
            KmEntrada,
            UsuarioPort,
            StatusPosto,
            StatusLav,
            StatusVist
          ) VALUES (
            @carretaId,
            @dataHoraBrasil,
            @horaFormatada,
            @bafometro,
            @motoristaId,
            @quilometragem,
            @usuarioId,
            '0', '0', '0'
          );

          SELECT SCOPE_IDENTITY() as id;
        `;

        const resultCarreta = await executeQuery(insertCarretaQuery, {
          carretaId,
          bafometro,
          motoristaId,
          quilometragem,
          usuarioId,
          registroPrincipalId
        });

        if (!resultCarreta || !resultCarreta[0]) {
          throw new Error('Falha ao inserir registro para a carreta');
        }

        registroCarretaId = resultCarreta[0].id;

        // Não criamos mais vinculação permanente entre cavalo mecânico e carreta
      }

      // Buscar o registro completo com informações relacionadas
      const registroQuery = `
        SELECT
          m.Id, m.Veiculo, m.DataEntrada, m.HoraEntrada,
          m.BafometroEnt, m.MotEntrada, m.KmEntrada,
          m.StatusPosto, m.StatusLav, m.StatusVist,
          f.Placa, f.Descricao AS DescricaoVeiculo, f.Tipo AS TipoVeiculo,
          c.Nome AS NomeMotorista, c.Matricula AS MatriculaMotorista
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        INNER JOIN Colaboradores c ON m.MotEntrada = c.Id
        WHERE m.Id = @id
      `;

      const registro = await executeQuery(registroQuery, { id: registroPrincipalId });

      // Se houver carreta, buscar suas informações também
      let registroCarreta = null;
      if (registroCarretaId) {
        const registroCarretaQuery = `
          SELECT
            m.Id, m.Veiculo,
            f.Placa, f.Descricao AS DescricaoVeiculo, f.Tipo AS TipoVeiculo
          FROM MovFrota m
          INNER JOIN Frota f ON m.Veiculo = f.Id
          WHERE m.Id = @id
        `;

        const resultadoCarreta = await executeQuery(registroCarretaQuery, { id: registroCarretaId });
        if (resultadoCarreta.length > 0) {
          registroCarreta = resultadoCarreta[0];
        }
      }

      return {
        message: 'Entrada registrada com sucesso',
        registro: registro[0],
        registroCarreta: registroCarreta
      };
    } catch (error) {
      // Log detalhado do erro para facilitar a depuração
      request.log.error({
        message: 'Erro ao registrar entrada de veículo',
        error: error.message,
        stack: error.stack,
        body: request.body
      });

      // Verificar se é um erro de validação
      if (error.validation) {
        return reply.code(400).send({
          error: 'Dados inválidos para registro de entrada',
          details: error.validation
        });
      }

      // Verificar se é um erro de banco de dados
      if (error.code && error.number) {
        return reply.code(500).send({
          error: 'Erro no banco de dados ao registrar entrada',
          details: `SQL Error: ${error.number} - ${error.message}`
        });
      }

      // Erro genérico
      return reply.code(500).send({
        error: 'Erro ao registrar entrada de veículo',
        message: error.message
      });
    }
  },

  /**
   * Registra saída de veículo
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados do registro atualizado
   */
  registrarSaida: async (request, reply) => {
    const {
      placa,
      bafometro,
      quilometragem,
      matricula_motorista_saida,
      placa_carreta
    } = request.body;

    // Flag para indicar se a carreta foi informada para saída
    // Usamos diretamente placa_carreta para verificar se a carreta foi informada

    try {
      // Buscar o ID do veículo na tabela Frota
      const veiculoQuery = `
        SELECT Id, Tipo FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }
      const veiculoId = veiculos[0].Id;

      // Buscar registro de entrada sem saída
      const entradaQuery = `
        SELECT TOP 1 Id FROM MovFrota
        WHERE Veiculo = @veiculoId
        AND DataSaida IS NULL
        ORDER BY DataEntrada DESC
      `;

      const entradas = await executeQuery(entradaQuery, { veiculoId });

      if (entradas.length === 0) {
        return reply.code(404).send({
          error: 'Não há registro de entrada para este veículo'
        });
      }

      const registroId = entradas[0].Id;

      // Verificar se todos os status estão OK
      const statusQuery = `
        SELECT
          StatusPosto,
          StatusLav,
          StatusVist
        FROM MovFrota
        WHERE Id = @id
      `;

      const statusResult = await executeQuery(statusQuery, { id: registroId });
      const status = statusResult[0];

      // ALTERAÇÃO TEMPORÁRIA: Ignorar verificação de status para permitir saída de veículos
      // Registrar no log que estamos ignorando as verificações
      request.log.warn({
        message: 'ALTERAÇÃO TEMPORÁRIA: Ignorando verificação de status para permitir saída do veículo',
        placa: placa,
        registroId: registroId,
        status: {
          abastecimento: isStatusOk(status.StatusPosto),
          lavagem: isStatusOk(status.StatusLav),
          vistoria: isStatusOk(status.StatusVist)
        }
      });

      // Código original comentado para referência futura
      /*
      // Verificar se todos os status são '1' ou 'OK' - campo char(1)
      const todosStatusOk =
        isStatusOk(status.StatusPosto) &&
        isStatusOk(status.StatusLav) &&
        isStatusOk(status.StatusVist);

      // Verificar se há OS pendentes para este veículo
      const osPendentesQuery = `
        SELECT COUNT(*) AS Total
        FROM OS
        WHERE MovFrota = @movFrotaId
        AND Status <> '4' -- Status 4 = Concluída
      `;

      const osPendentesResult = await executeQuery(osPendentesQuery, { movFrotaId: registroId });
      const temOSPendente = osPendentesResult[0].Total > 0;

      if (!todosStatusOk || temOSPendente) {
        const motivos = [];
        if (!isStatusOk(status.StatusPosto)) motivos.push('Abastecimento pendente');
        if (!isStatusOk(status.StatusLav)) motivos.push('Lavagem pendente');
        if (!isStatusOk(status.StatusVist)) motivos.push('Vistoria pendente');
        if (temOSPendente) motivos.push('Ordens de Serviço pendentes');

        return reply.code(400).send({
          error: 'Veículo não está liberado para saída. Verifique os status pendentes.',
          motivos: motivos,
          status: {
            abastecimento: isStatusOk(status.StatusPosto),
            lavagem: isStatusOk(status.StatusLav),
            vistoria: isStatusOk(status.StatusVist),
            semOSPendente: !temOSPendente
          }
        });
      }
      */
      // Não verificamos mais veículo vinculado, pois não há mais vinculação permanente
      // Cada veículo (cavalo mecânico e carreta) é tratado de forma independente

      // Obter o ID do usuário atual a partir do token JWT
      const usuarioId = request.user.id;

      // Buscar ID do motorista pela matrícula
      let motoristaId = null;
      if (matricula_motorista_saida) {
        const motoristaQuery = `
          SELECT Id FROM Colaboradores
          WHERE RTRIM(Matricula) = @matricula_motorista_saida
        `;

        const motoristas = await executeQuery(motoristaQuery, { matricula_motorista_saida });

        if (motoristas.length === 0) {
          return reply.code(404).send({
            error: 'Motorista não encontrado no cadastro'
          });
        }

        motoristaId = motoristas[0].Id;
      }

      // Não atualizamos mais automaticamente a carreta vinculada
      // Cada veículo (cavalo mecânico e carreta) deve ter sua saída registrada independentemente

      // Definir a query para atualização do registro com dados de saída (será executada depois de verificar a carreta)
      // Também preencher as datas dos serviços não realizados com a data de saída
      const updateQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
        DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
        DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

        -- Verificar quais serviços não foram realizados
        DECLARE @statusPosto CHAR(1), @statusLav CHAR(1), @statusVist CHAR(1);
        SELECT
          @statusPosto = StatusPosto,
          @statusLav = StatusLav,
          @statusVist = StatusVist
        FROM MovFrota
        WHERE Id = @id;

        -- Atualizar o registro com dados de saída
        UPDATE MovFrota SET
          DataSaida = @dataHoraBrasil,
          HoraSaida = @horaFormatada,
          KmSaida = @quilometragem,
          BafometroSaida = @bafometro,
          UsuarioSaida = @usuarioId,
          MotoristaSaida = @motoristaId,
          -- Preencher datas de serviços não realizados
          DataPosto = CASE WHEN @statusPosto = '0' THEN @dataHoraBrasil ELSE DataPosto END,
          HoraPosto = CASE WHEN @statusPosto = '0' THEN @horaFormatada ELSE HoraPosto END,
          StatusPosto = CASE WHEN @statusPosto = '0' THEN '1' ELSE StatusPosto END,
          UsuarioPosto = CASE WHEN @statusPosto = '0' THEN @usuarioId ELSE UsuarioPosto END,

          DataLav = CASE WHEN @statusLav = '0' THEN @dataHoraBrasil ELSE DataLav END,
          HoraLav = CASE WHEN @statusLav = '0' THEN @horaFormatada ELSE HoraLav END,
          StatusLav = CASE WHEN @statusLav = '0' THEN '1' ELSE StatusLav END,
          UsuarioLav = CASE WHEN @statusLav = '0' THEN @usuarioId ELSE UsuarioLav END,

          DataVist = CASE WHEN @statusVist = '0' THEN @dataHoraBrasil ELSE DataVist END,
          HoraVist = CASE WHEN @statusVist = '0' THEN @horaFormatada ELSE HoraVist END,
          StatusVist = CASE WHEN @statusVist = '0' THEN '1' ELSE StatusVist END,
          UsuarioVist = CASE WHEN @statusVist = '0' THEN @usuarioId ELSE UsuarioVist END
        WHERE Id = @id;
      `;

      // Definir a query para buscar informações completas após a atualização
      const registroQuery = `
        SELECT
          m.Id, m.Veiculo, m.DataEntrada, m.HoraEntrada,
          m.BafometroEnt, m.MotEntrada, m.KmEntrada,
          m.StatusPosto, m.StatusLav, m.StatusVist,
          m.DataSaida, m.HoraSaida, m.KmSaida, m.BafometroSaida,
          f.Placa, f.Descricao AS DescricaoVeiculo, f.Tipo AS TipoVeiculo,
          c.Nome AS NomeMotorista, c.Matricula AS MatriculaMotorista
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        INNER JOIN Colaboradores c ON m.MotEntrada = c.Id
        WHERE m.Id = @id
      `;

      // Verificar se o veículo é um cavalo (tipo 1 ou L)
      const veiculoTipoQuery = `
        SELECT Tipo FROM Frota
        WHERE Id = (SELECT Veiculo FROM MovFrota WHERE Id = @registroId)
      `;

      const veiculoTipoResult = await executeQuery(veiculoTipoQuery, { registroId });
      const tipoVeiculoTrim = veiculoTipoResult[0].Tipo ? veiculoTipoResult[0].Tipo.trim() : '';
      const isCavalo = tipoVeiculoTrim === '1' || tipoVeiculoTrim === 'L';

      // Se for um cavalo, verificar se há carreta vinculada no pátio, mesmo que não tenha sido informada
      if (isCavalo && !placa_carreta) {
        // Verificar se há carreta no pátio que possa estar vinculada a este cavalo
        const carretasNoPatio = `
          SELECT f.Id, f.Placa, m.Id as MovFrotaId
          FROM Frota f
          INNER JOIN MovFrota m ON f.Id = m.Veiculo
          WHERE f.Tipo = '4' -- Tipo 4 = Carreta
          AND m.DataSaida IS NULL
        `;

        const carretasDisponiveis = await executeQuery(carretasNoPatio);

        // Se há carreta no pátio e o usuário não marcou para sair com carreta
        if (carretasDisponiveis.length > 0) {
          // Apenas logar para fins de auditoria, mas permitir a saída do cavalo sem carreta
          request.log.info({
            message: 'Cavalo saindo sem carreta, embora existam carretas disponíveis no pátio',
            placa_cavalo: placa,
            carretas_disponiveis: carretasDisponiveis.map(c => c.Placa)
          });
        }
      }

      // Se foi informada uma placa de carreta, verificar se está apta a sair e registrar a saída dela também
      let registroCarreta = null;
      let carretaId = null;
      let registroCarretaId = null;

      if (placa_carreta) {
        try {
          // Buscar o ID da carreta na tabela Frota
          const carretaQuery = `
            SELECT Id FROM Frota
            WHERE RTRIM(Placa) = @placa_carreta
          `;

          const carretas = await executeQuery(carretaQuery, { placa_carreta });

          if (carretas.length === 0) {
            return reply.code(404).send({
              error: 'Carreta não encontrada no cadastro'
            });
          }

          carretaId = carretas[0].Id;

          // Buscar registro de entrada sem saída para a carreta
          const carretaEntradaQuery = `
            SELECT TOP 1 Id FROM MovFrota
            WHERE Veiculo = @carretaId
            AND DataSaida IS NULL
            ORDER BY DataEntrada DESC
          `;

          const carretaEntradas = await executeQuery(carretaEntradaQuery, { carretaId });

          if (carretaEntradas.length === 0) {
            return reply.code(404).send({
              error: 'Não há registro de entrada para esta carreta'
            });
          }

          registroCarretaId = carretaEntradas[0].Id;

          // Verificar se a carreta está apta a sair (status de lavagem e vistoria)
          const carretaStatusQuery = `
            SELECT
              StatusLav,
              StatusVist
            FROM MovFrota
            WHERE Id = @id
          `;

          const carretaStatusResult = await executeQuery(carretaStatusQuery, { id: registroCarretaId });
          const carretaStatus = carretaStatusResult[0];

          // ALTERAÇÃO TEMPORÁRIA: Ignorar verificação de status para permitir saída da carreta
          // Registrar no log que estamos ignorando as verificações
          request.log.warn({
            message: 'ALTERAÇÃO TEMPORÁRIA: Ignorando verificação de status para permitir saída da carreta',
            placa_carreta: placa_carreta,
            registroCarretaId: registroCarretaId,
            status: {
              lavagem: carretaStatus.StatusLav === '1',
              vistoria: carretaStatus.StatusVist === '1'
            }
          });

          // Código original comentado para referência futura
          /*
          // Verificar se os status de lavagem e vistoria são '1' (OK) - campo char(1)
          const carretaStatusOk =
            carretaStatus.StatusLav === '1' &&
            carretaStatus.StatusVist === '1';

          // Verificar se há OS pendentes para a carreta
          const carretaOsPendentesQuery = `
            SELECT COUNT(*) AS Total
            FROM OS
            WHERE MovFrota = @movFrotaId
            AND Status <> '4' -- Status 4 = Concluída
          `;

          const carretaOsPendentesResult = await executeQuery(carretaOsPendentesQuery, { movFrotaId: registroCarretaId });
          const carretaTemOSPendente = carretaOsPendentesResult[0].Total > 0;

          // Se a carreta não estiver apta a sair e o usuário marcou para sair com ela, impedir a saída
          if (!carretaStatusOk || carretaTemOSPendente) {
            const motivos = [];
            if (carretaStatus.StatusLav !== '1') motivos.push('Lavagem pendente na carreta');
            if (carretaStatus.StatusVist !== '1') motivos.push('Vistoria pendente na carreta');
            if (carretaTemOSPendente) motivos.push('Ordens de Serviço pendentes na carreta');

            return reply.code(400).send({
              error: 'Carreta não está liberada para saída. Verifique os status pendentes ou desmarque a opção de sair com carreta.',
              motivos: motivos,
              status: {
                lavagem: carretaStatus.StatusLav === '1',
                vistoria: carretaStatus.StatusVist === '1',
                semOSPendente: !carretaTemOSPendente
              }
            });
          }
          */

          // Atualizar registro da carreta com dados de saída
          // Também preencher as datas dos serviços não realizados com a data de saída
          const updateCarretaQuery = `
            -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
            DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
            DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
            DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

            -- Verificar quais serviços não foram realizados
            DECLARE @statusLav CHAR(1), @statusVist CHAR(1);
            SELECT
              @statusLav = StatusLav,
              @statusVist = StatusVist
            FROM MovFrota
            WHERE Id = @id;

            UPDATE MovFrota SET
              DataSaida = @dataHoraBrasil,
              HoraSaida = @horaFormatada,
              UsuarioSaida = @usuarioId,
              MotoristaSaida = @motoristaId,
              -- Preencher datas de serviços não realizados (carreta não precisa de abastecimento)
              DataLav = CASE WHEN @statusLav = '0' THEN @dataHoraBrasil ELSE DataLav END,
              HoraLav = CASE WHEN @statusLav = '0' THEN @horaFormatada ELSE HoraLav END,
              StatusLav = CASE WHEN @statusLav = '0' THEN '1' ELSE StatusLav END,
              UsuarioLav = CASE WHEN @statusLav = '0' THEN @usuarioId ELSE UsuarioLav END,

              DataVist = CASE WHEN @statusVist = '0' THEN @dataHoraBrasil ELSE DataVist END,
              HoraVist = CASE WHEN @statusVist = '0' THEN @horaFormatada ELSE HoraVist END,
              StatusVist = CASE WHEN @statusVist = '0' THEN '1' ELSE StatusVist END,
              UsuarioVist = CASE WHEN @statusVist = '0' THEN @usuarioId ELSE UsuarioVist END
            WHERE Id = @id;
          `;

          await executeQuery(updateCarretaQuery, {
            id: registroCarretaId,
            usuarioId,
            motoristaId
          });

          // Buscar informações da carreta após a atualização
          const registroCarretaQuery = `
            SELECT
              m.Id, m.Veiculo,
              f.Placa, f.Descricao AS DescricaoVeiculo, f.Tipo AS TipoVeiculo
            FROM MovFrota m
            INNER JOIN Frota f ON m.Veiculo = f.Id
            WHERE m.Id = @id
          `;

          const resultadoCarreta = await executeQuery(registroCarretaQuery, { id: registroCarretaId });
          if (resultadoCarreta.length > 0) {
            registroCarreta = resultadoCarreta[0];
          }
        } catch (carretaError) {
          request.log.error({
            message: 'Erro ao registrar saída da carreta',
            error: carretaError.message,
            stack: carretaError.stack,
            placa_carreta
          });

          // Não interromper o fluxo principal se houver erro com a carreta
          // Apenas logar o erro e continuar
        }
      }

      // Agora que verificamos a carreta (se houver), podemos atualizar o registro do cavalo
      await executeQuery(updateQuery, {
        id: registroId,
        bafometro,
        quilometragem,
        usuarioId,
        motoristaId
      });

      // Buscar informações completas após a atualização
      const result = await executeQuery(registroQuery, { id: registroId });

      // Processar o registro usando a função utilitária
      const registroProcessado = processarVeiculo(result[0]);

      return {
        message: 'Saída registrada com sucesso',
        registro: registroProcessado,
        registroCarreta
      };
    } catch (error) {
      // Log detalhado do erro para facilitar a depuração
      request.log.error({
        message: 'Erro ao registrar saída de veículo',
        error: error.message,
        stack: error.stack,
        body: request.body
      });

      // Verificar se é um erro de validação
      if (error.validation) {
        return reply.code(400).send({
          error: 'Dados inválidos para registro de saída',
          details: error.validation
        });
      }

      // Verificar se é um erro de banco de dados
      if (error.code && error.number) {
        return reply.code(500).send({
          error: 'Erro no banco de dados ao registrar saída',
          details: `SQL Error: ${error.number} - ${error.message}`
        });
      }

      // Erro genérico
      return reply.code(500).send({
        error: 'Erro ao registrar saída de veículo',
        message: error.message
      });
    }
  },

  /**
   * Lista veículos no pátio
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de veículos no pátio
   */
  listarVeiculosNoPatio: async (request, reply) => {
    try {
      const query = `
        SELECT
          m.Id,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          f.Tipo AS TipoVeiculo,
          c.Nome AS NomeMotorista,
          c.Matricula AS MatriculaMotorista,
          m.DataEntrada,
          m.HoraEntrada,
          m.KmEntrada,
          m.StatusPosto,
          m.StatusLav,
          m.StatusVist,
          CASE
            WHEN (m.StatusPosto = '1' OR m.StatusPosto = 'OK') AND (m.StatusLav = '1' OR m.StatusLav = 'OK') AND (m.StatusVist = '1' OR m.StatusVist = 'OK')
            THEN 1
            ELSE 0
          END as ProntoParaSaida
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        INNER JOIN Colaboradores c ON m.MotEntrada = c.Id
        WHERE m.DataSaida IS NULL
        ORDER BY m.DataEntrada DESC, m.HoraEntrada DESC
      `;

      const veiculos = await executeQuery(query);

      // Log para verificar os status dos veículos antes do processamento
      request.log.info({
        message: 'Status dos veículos no pátio antes do processamento',
        veiculos: veiculos.map(v => ({
          placa: v.Placa ? v.Placa.trim() : '',
          statusPosto: v.StatusPosto,
          statusLav: v.StatusLav,
          statusVist: v.StatusVist
        }))
      });

      // Processar os resultados usando a função utilitária
      const veiculosProcessados = processarVeiculos(veiculos);

      // Obter a data e hora atual do servidor SQL para cálculo correto do tempo no pátio
      // Converter para o fuso horário do Brasil (UTC-3)
      const dataHoraQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        SELECT DATEADD(HOUR, -3, GETUTCDATE()) AS dataHoraAtual
      `;
      const dataHoraResult = await executeQuery(dataHoraQuery);
      const dataHoraAtual = dataHoraResult[0].dataHoraAtual;

      return {
        total: veiculosProcessados.length,
        veiculos: veiculosProcessados,
        dataHoraAtual: dataHoraAtual
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar veículos no pátio'
      });
    }
  },

  /**
   * Consulta veículo por placa
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados do veículo
   */
  /**
   * Verifica se um veículo está liberado para saída
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Status de liberação do veículo
   */
  verificarLiberacao: async (request, reply) => {
    const { id } = request.params;

    try {
      // Verificar se o ID foi fornecido
      if (!id) {
        return reply.code(400).send({
          error: 'ID do registro não fornecido'
        });
      }

      // Verificar se o registro existe
      const registroQuery = `
        SELECT
          Id,
          Veiculo,
          StatusPosto,
          StatusLav,
          StatusVist
        FROM MovFrota
        WHERE Id = @id
        AND DataSaida IS NULL
      `;

      const registros = await executeQuery(registroQuery, { id });

      if (registros.length === 0) {
        return reply.code(404).send({
          error: 'Registro não encontrado ou veículo já saiu do pátio'
        });
      }

      const registro = registros[0];
      const veiculoId = registro.Veiculo;

      // ALTERAÇÃO TEMPORÁRIA: Ignorar verificação de status para permitir saída de veículos
      // Obter os status reais para registro no log
      const statusPosto = isStatusOk(registro.StatusPosto);
      const statusLav = isStatusOk(registro.StatusLav);
      const statusVist = isStatusOk(registro.StatusVist);

      // Verificar se há OS pendentes para este veículo (apenas para log)
      const osPendentesQuery = `
        SELECT COUNT(*) AS Total
        FROM OS
        WHERE MovFrota = @movFrotaId
        AND Status <> '4' -- Status 4 = Concluída
      `;

      const osPendentesResult = await executeQuery(osPendentesQuery, { movFrotaId: id });
      const temOSPendente = osPendentesResult[0].Total > 0;

      // ALTERAÇÃO TEMPORÁRIA: Sempre retornar liberado = true
      const liberado = true;

      // Registrar no log que estamos ignorando as verificações
      request.log.warn({
        message: 'ALTERAÇÃO TEMPORÁRIA: Ignorando verificação de status para permitir saída do veículo',
        registroId: id,
        statusReal: {
          abastecimento: statusPosto,
          lavagem: statusLav,
          vistoria: statusVist,
          semOSPendente: !temOSPendente
        },
        liberadoForçado: liberado
      });

      // Não atualizamos mais os status para '1' automaticamente
      // Apenas registramos no log que estamos ignorando a verificação de status

      // Preparar lista de motivos (apenas para informação, não bloqueia mais)
      const motivos = [];
      if (!statusPosto) motivos.push('Abastecimento pendente');
      if (!statusLav) motivos.push('Lavagem pendente');
      if (!statusVist) motivos.push('Vistoria pendente');
      if (temOSPendente) motivos.push('Ordens de Serviço pendentes');

      return {
        liberado,
        motivos,
        status: {
          abastecimento: statusPosto,
          lavagem: statusLav,
          vistoria: statusVist,
          semOSPendente: !temOSPendente
        }
      };
    } catch (error) {
      request.log.error({
        message: 'Erro ao verificar liberação de veículo',
        error: error.message,
        stack: error.stack,
        params: request.params
      });

      return reply.code(500).send({
        error: 'Erro ao verificar liberação do veículo',
        message: error.message
      });
    }
  },

  consultarVeiculo: async (request, reply) => {
    const { placa } = request.params;

    try {
      // Buscar o veículo na tabela Frota
      const veiculoQuery = `
        SELECT
          Id,
          Placa,
          Descricao,
          Tipo,
          Ano,
          Aquisicao
        FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }

      // Processar o resultado para formatar nomes e remover espaços
      const veiculo = {
        ...veiculos[0],
        Placa: veiculos[0].Placa ? veiculos[0].Placa.trim() : '',
        Descricao: veiculos[0].Descricao ? veiculos[0].Descricao.trim() : '',
        Tipo: veiculos[0].Tipo ? veiculos[0].Tipo.trim() : ''
      };

      return {
        veiculo,
        tipo: veiculo.Tipo,
        descricao: veiculo.Descricao
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao consultar veículo'
      });
    }
  }
};

module.exports = portariaController;