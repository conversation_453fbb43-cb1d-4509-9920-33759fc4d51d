# Script PowerShell para atualizar automaticamente o arquivo db_structure.md
# Autor: Rafael
# Data: 06/06/2025

Write-Host "=== Atualizador de Estrutura do Banco de Dados BWA ===" -ForegroundColor Cyan
Write-Host ""

# Verificar se o Node.js está instalado
try {
    $nodeVersion = node -v
    Write-Host "Node.js encontrado: $nodeVersion" -ForegroundColor Green
}
catch {
    Write-Host "Node.js não encontrado. Por favor, instale o Node.js antes de continuar." -ForegroundColor Red
    Write-Host "Você pode baixá-lo em: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Definir o diretório raiz do projeto
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$rootPath = Split-Path -Parent $scriptPath
$backendPath = Join-Path -Path $rootPath -ChildPath "backend"

# Verificar se o arquivo .env existe
$envPath = Join-Path -Path $backendPath -ChildPath ".env"
if (-not (Test-Path $envPath)) {
    Write-Host "Arquivo .env não encontrado em: $envPath" -ForegroundColor Red
    Write-Host "Execute primeiro o script extract-db-structure.ps1 para configurar o ambiente." -ForegroundColor Yellow
    exit 1
}

# Verificar se as dependências estão instaladas
$nodeModulesPath = Join-Path -Path $backendPath -ChildPath "node_modules"
if (-not (Test-Path $nodeModulesPath)) {
    Write-Host "Dependências não encontradas. Instalando..." -ForegroundColor Yellow
    Set-Location $backendPath
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Erro ao instalar dependências. Saindo..." -ForegroundColor Red
        exit 1
    }
    Set-Location $rootPath
}

# Executar script de extração
Write-Host "Extraindo estrutura atual do banco de dados..." -ForegroundColor Cyan
Set-Location $backendPath
$extractScriptPath = Join-Path -Path $backendPath -ChildPath "get-db-structure.js"
node $extractScriptPath

if ($LASTEXITCODE -ne 0) {
    Write-Host "Erro ao extrair estrutura do banco de dados. Verifique as mensagens de erro acima." -ForegroundColor Red
    Set-Location $rootPath
    exit 1
}

Set-Location $rootPath

# Verificar se o arquivo de saída foi gerado
$outputPath = Join-Path -Path $rootPath -ChildPath "db_structure_extracted.md"
if (Test-Path $outputPath) {
    Write-Host "Estrutura extraída com sucesso!" -ForegroundColor Green
    
    # Fazer backup do arquivo atual
    $mainStructurePath = Join-Path -Path $rootPath -ChildPath "db_structure.md"
    $backupPath = Join-Path -Path $rootPath -ChildPath "db_structure_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    
    if (Test-Path $mainStructurePath) {
        Copy-Item $mainStructurePath $backupPath
        Write-Host "Backup criado: $backupPath" -ForegroundColor Yellow
    }
    
    # Substituir o arquivo principal
    Copy-Item $outputPath $mainStructurePath -Force
    Write-Host "Arquivo db_structure.md atualizado com sucesso!" -ForegroundColor Green
    
    # Remover arquivo temporário
    Remove-Item $outputPath
    Write-Host "Arquivo temporário removido." -ForegroundColor Gray
    
    Write-Host ""
    Write-Host "Atualização concluída com sucesso!" -ForegroundColor Green
    Write-Host "O arquivo db_structure.md foi atualizado com a estrutura atual do banco de dados." -ForegroundColor Green
}
else {
    Write-Host "Arquivo de saída não encontrado. Verifique as mensagens de erro acima." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Processo concluído." -ForegroundColor Cyan
