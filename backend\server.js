require('dotenv').config();
const fastify = require('fastify')({ logger: true });
const path = require('path');
const fs = require('fs');

// Importar o módulo de conexão com o banco de dados
const { connectDB } = require('./models/db');

// Registrar plugins
fastify.register(require('@fastify/cors'), {
  origin: '*',
  methods: ['GET', 'PUT', 'POST', 'DELETE']
});

fastify.register(require('@fastify/jwt'), {
  secret: process.env.JWT_SECRET || 'bwatransportesecretkey'
});

// Middleware de autenticação
fastify.decorate('authenticate', async (request, reply) => {
  try {
    await request.jwtVerify();
  } catch (err) {
    reply.code(401).send({ error: 'Não autorizado' });
  }
});

// Registrar rotas
const routesPath = path.join(__dirname, 'routes');
fs.readdirSync(routesPath).forEach(file => {
  if (file.endsWith('.js')) {
    const route = require(path.join(routesPath, file));
    fastify.register(route);
  }
});

// Rota de verificação de saúde
fastify.get('/health', async (request, reply) => {
  return { status: 'ok', timestamp: new Date() };
});

// Iniciar o servidor
const start = async () => {
  try {
    // Testar conexão com o banco de dados
    await connectDB();
    fastify.log.info('Conexão com o banco de dados estabelecida com sucesso');

    // Iniciar o servidor Fastify
    const port = process.env.PORT || 3000;
    await fastify.listen({ port, host: '0.0.0.0' });
    fastify.log.info(`Servidor rodando em ${fastify.server.address().port}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();