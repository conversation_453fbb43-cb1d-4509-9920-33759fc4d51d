const lavagemController = require('../controllers/lavagemController');

/**
 * <PERSON><PERSON><PERSON>m
 * @param {Object} fastify - Instância do Fastify
 * @param {Object} options - Opções do plugin
 */
async function lavagemRoutes(fastify, options) {
  // Schema para validação de registro de lavagem
  const lavagemSchema = {
    body: {
      type: 'object',
      required: ['placa'],
      properties: {
        placa: { type: 'string' },
        observacao: { type: 'string' }
      }
    }
  };

  // Todas as rotas abaixo requerem autenticação
  fastify.register(async function (fastify) {
    // Aplicar middleware de autenticação a todas as rotas neste contexto
    fastify.addHook('preHandler', fastify.authenticate);

    // Rota para registrar lavagem
    fastify.post('/api/lavagem', { schema: lavagemSchema }, lavagemController.registrarLavagem);

    // Rota para listar lavagens de um veículo
    fastify.get('/api/lavagem/veiculo/:placa', {
      schema: {
        params: {
          type: 'object',
          required: ['placa'],
          properties: {
            placa: { type: 'string' }
          }
        }
      }
    }, lavagemController.listarLavagens);

    // Rota para listar veículos pendentes de lavagem
    fastify.get('/api/lavagem/pendentes', lavagemController.listarPendentes);

    // Rota para finalizar lavagem (apenas para usuários Master)
    fastify.put('/api/lavagem/:id/finalizar', {
      schema: {
        params: {
          type: 'object',
          required: ['id'],
          properties: {
            id: { type: 'number' }
          }
        }
      }
    }, lavagemController.finalizarLavagem);
  });
}

module.exports = lavagemRoutes;