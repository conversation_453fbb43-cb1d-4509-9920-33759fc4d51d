# Guia de Gerenciamento do Serviço PM2 - BWA Transportes API

Este documento descreve os procedimentos para gerenciar o serviço PM2 que executa a API da BWA Transportes.

## Verificação de Status

### Verificar status do serviço PM2
```powershell
Get-Service PM2
```

### Verificar status das aplicações gerenciadas pelo PM2
```powershell
pm2 status
```
Este comando mostra uma tabela com todas as aplicações gerenciadas, incluindo status, uso de CPU, memória e tempo de execução.

## Gerenciamento do Serviço

### Iniciar o serviço PM2
```powershell
Start-Service PM2
```

### Parar o serviço PM2
```powershell
Stop-Service PM2
```

### Reiniciar o serviço PM2
```powershell
Restart-Service PM2
```

### Configurar o serviço para iniciar automaticamente
```powershell
Set-Service PM2 -StartupType Automatic
```

## Gerenciamento da Aplicação

### Iniciar a aplicação
```powershell
pm2 start server.js --name "bwa-api"
```

### Parar a aplicação
```powershell
pm2 stop bwa-api
```

### Reiniciar a aplicação
```powershell
pm2 restart bwa-api
```

### Recarregar a aplicação (sem downtime)
```powershell
pm2 reload bwa-api
```

### Remover a aplicação da lista do PM2
```powershell
pm2 delete bwa-api
```

### Salvar a configuração atual (importante após alterações)
```powershell
pm2 save
```

## Monitoramento e Logs

### Visualizar logs em tempo real
```powershell
pm2 logs bwa-api
```

### Visualizar logs com número limitado de linhas
```powershell
pm2 logs bwa-api --lines 200
```

### Limpar logs
```powershell
pm2 flush
```

### Monitoramento em tempo real (interface interativa)
```powershell
pm2 monit
```

### Informações detalhadas sobre a aplicação
```powershell
pm2 show bwa-api
```

## Solução de Problemas

### Verificar erros no serviço PM2
```powershell
Get-EventLog -LogName Application -Source "PM2" -Newest 20
```

### Reiniciar o PM2 e todas as aplicações
```powershell
pm2 resurrect
```

### Verificar uso de memória e CPU
```powershell
pm2 list
```

### Verificar se a porta está em uso
```powershell
netstat -ano | findstr :3000
```

## Atualização da Aplicação

### Procedimento para atualizar a aplicação
1. Faça backup dos arquivos atuais:
   ```powershell
   Copy-Item -Path C:\bwa-transportes-app\backend -Destination C:\bwa-transportes-app\backend-backup -Recurse
   ```

2. Atualize os arquivos da aplicação (git pull ou cópia manual)

3. Reinicie a aplicação:
   ```powershell
   pm2 restart bwa-api
   ```

4. Verifique os logs para confirmar que a aplicação iniciou corretamente:
   ```powershell
   pm2 logs bwa-api --lines 50
   ```

## Comandos Avançados

### Iniciar a aplicação com variáveis de ambiente específicas
```powershell
pm2 start server.js --name "bwa-api" --env production
```

### Configurar número de instâncias (cluster mode)
```powershell
pm2 start server.js --name "bwa-api" -i 2
```

### Iniciar com arquivo de configuração
```powershell
pm2 start ecosystem.config.js
```

### Atualizar o PM2 para a versão mais recente
```powershell
npm install -g pm2@latest
```

## Localização de Arquivos Importantes

- **Arquivos de Log do PM2**: `C:\Users\<USER>\.pm2\logs\`
- **Arquivo de Configuração do PM2**: `C:\Users\<USER>\.pm2\module_conf.json`
- **Diretório da Aplicação**: `C:\bwa-transportes-app\backend\`
- **Arquivo .env**: `C:\bwa-transportes-app\backend\.env`

## Contatos para Suporte

Em caso de problemas que não possam ser resolvidos com este guia, entre em contato com:

- **Email**: <EMAIL>
- **Telefone**: (11) 1234-5678

---

*Documento criado em: 2023-11-15*  
*Última atualização: 2023-11-15*