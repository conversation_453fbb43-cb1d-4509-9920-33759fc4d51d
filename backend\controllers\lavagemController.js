const { executeQuery } = require('../models/db');

/**
 * Controlador de Lavagem
 * Gerencia registros de lavagem de veículos
 */
const lavagemController = {
  /**
   * Registra lavagem de veículo
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados da lavagem registrada
   */
  registrarLavagem: async (request, reply) => {
    const {
      placa,
      observacao
    } = request.body;

    try {
      // Buscar o ID do veículo na tabela Frota
      const veiculoQuery = `
        SELECT Id FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }

      const veiculoId = veiculos[0].Id;

      // Verificar se o veículo está no pátio e obter seu status atual
      const veiculoPatioQuery = `
        SELECT TOP 1 Id, StatusLav, UsuarioLav FROM MovFrota
        WHERE Veiculo = @veiculoId
        AND DataSaida IS NULL
        ORDER BY DataEntrada DESC
      `;

      const veiculosPatio = await executeQuery(veiculoPatioQuery, { veiculoId });

      if (veiculosPatio.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no pátio'
        });
      }

      const movimentacaoId = veiculosPatio[0].Id;
      const statusLavAtual = veiculosPatio[0].StatusLav;

      // Obter o ID do usuário atual a partir do token JWT
      const usuarioId = request.user.id;

      // Obter o tipo de usuário para determinar o status
      const tipoUsuario = request.user.tipo;

      // Verificar se é usuário Master (tipo 0, M ou 1)
      const isMaster = tipoUsuario === '0' || tipoUsuario === 'M' || tipoUsuario === '1';

      // Definir o status com base no tipo de usuário
      // Master pode finalizar diretamente (status 1)
      // Usuário Lavagem apenas marca como concluído (status 2)
      const statusLav = isMaster ? '1' : '2';

      // Atualizar registro na MovFrota com dados de lavagem
      // Usar o fuso horário do Brasil (UTC-3)
      let updateQuery = '';

      if (isMaster) {
        // Verificar se o veículo já está com status 2 (concluído)
        // Se estiver, não atualizar o UsuarioLav para preservar o usuário original
        if (statusLavAtual === '2') {
          // Query para usuário Master finalizando uma lavagem já concluída (não atualiza UsuarioLav)
          updateQuery = `
            -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
            DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
            DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
            DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

            -- Atualizar apenas StatusLav, DataLav e HoraLav, sem modificar UsuarioLav
            UPDATE MovFrota SET
              StatusLav = @statusLav,
              DataLav = @dataHoraBrasil,
              HoraLav = @horaFormatada
            WHERE Id = @movimentacaoId;

            SELECT
              m.Id,
              f.Placa,
              f.Descricao AS DescricaoVeiculo,
              m.DataEntrada,
              m.StatusLav,
              m.DataLav,
              m.HoraLav,
              m.UsuarioLav,
              u.Usuario AS NomeUsuario
            FROM MovFrota m
            INNER JOIN Frota f ON m.Veiculo = f.Id
            LEFT JOIN Usuarios u ON m.UsuarioLav = u.Id
            WHERE m.Id = @movimentacaoId
          `;
        } else {
          // Query para usuário Master (atualiza data, hora e usuário)
          updateQuery = `
            -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
            DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
            DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
            DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

            UPDATE MovFrota SET
              StatusLav = @statusLav,
              UsuarioLav = @usuarioId,
              DataLav = @dataHoraBrasil,
              HoraLav = @horaFormatada
            WHERE Id = @movimentacaoId;

            SELECT
              m.Id,
              f.Placa,
              f.Descricao AS DescricaoVeiculo,
              m.DataEntrada,
              m.StatusLav,
              m.DataLav,
              m.HoraLav,
              m.UsuarioLav,
              u.Usuario AS NomeUsuario
            FROM MovFrota m
            INNER JOIN Frota f ON m.Veiculo = f.Id
            LEFT JOIN Usuarios u ON m.UsuarioLav = u.Id
            WHERE m.Id = @movimentacaoId
          `;
        }
      } else {
        // Query para usuário regular (não atualiza data e hora)
        updateQuery = `
          -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
          DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
          DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
          DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

          UPDATE MovFrota SET
            StatusLav = @statusLav,
            UsuarioLav = @usuarioId
          WHERE Id = @movimentacaoId;

          SELECT
            m.Id,
            f.Placa,
            f.Descricao AS DescricaoVeiculo,
            m.DataEntrada,
            m.StatusLav,
            m.DataLav,
            m.HoraLav,
            m.UsuarioLav,
            u.Usuario AS NomeUsuario
          FROM MovFrota m
          INNER JOIN Frota f ON m.Veiculo = f.Id
          LEFT JOIN Usuarios u ON m.UsuarioLav = u.Id
          WHERE m.Id = @movimentacaoId
        `;
      }

      const result = await executeQuery(updateQuery, {
        movimentacaoId,
        usuarioId,
        statusLav
      });

      if (!result || !result[0]) {
        throw new Error('Falha ao registrar lavagem');
      }

      return {
        message: 'Lavagem registrada com sucesso',
        lavagem: {
          id: result[0].Id,
          placa: result[0].Placa.trim(),
          descricaoVeiculo: result[0].DescricaoVeiculo ? result[0].DescricaoVeiculo.trim() : '',
          dataLavagem: result[0].DataLav,
          horaLavagem: result[0].HoraLav,
          usuarioId: result[0].UsuarioLav,
          nomeUsuario: result[0].NomeUsuario ? result[0].NomeUsuario.trim() : '',
          observacao: observacao || ''
        }
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao registrar lavagem'
      });
    }
  },

  /**
   * Lista histórico de lavagens de um veículo
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de lavagens
   */
  listarLavagens: async (request, reply) => {
    const { placa } = request.params;

    try {
      // Buscar o ID do veículo na tabela Frota
      const veiculoQuery = `
        SELECT Id FROM Frota
        WHERE RTRIM(Placa) = @placa
      `;

      const veiculos = await executeQuery(veiculoQuery, { placa });

      if (veiculos.length === 0) {
        return reply.code(404).send({
          error: 'Veículo não encontrado no cadastro'
        });
      }

      const veiculoId = veiculos[0].Id;

      // Consultar registros de lavagem na MovFrota
      const query = `
        SELECT
          m.Id,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          m.DataEntrada,
          m.HoraEntrada,
          m.StatusLav,
          m.DataLav,
          m.HoraLav,
          m.UsuarioLav,
          u.Usuario AS NomeUsuario,
          c.Nome AS NomeMotorista
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        LEFT JOIN Usuarios u ON m.UsuarioLav = u.Id
        LEFT JOIN Colaboradores c ON m.MotEntrada = c.Id
        WHERE m.Veiculo = @veiculoId
          AND (m.StatusLav = '1' OR m.StatusLav = 'OK')
        ORDER BY m.DataLav DESC, m.HoraLav DESC
      `;

      const lavagens = await executeQuery(query, { veiculoId });

      // Processar os resultados para formatar campos
      const lavagensProcessadas = lavagens.map(l => ({
        id: l.Id,
        placa: l.Placa.trim(),
        descricaoVeiculo: l.DescricaoVeiculo ? l.DescricaoVeiculo.trim() : '',
        dataEntrada: l.DataEntrada,
        horaEntrada: l.HoraEntrada,
        dataLavagem: l.DataLav,
        horaLavagem: l.HoraLav,
        usuarioId: l.UsuarioLav,
        nomeUsuario: l.NomeUsuario ? l.NomeUsuario.trim() : '',
        nomeMotorista: l.NomeMotorista ? l.NomeMotorista.trim() : ''
      }));

      return {
        total: lavagensProcessadas.length,
        lavagens: lavagensProcessadas
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar lavagens'
      });
    }
  },

  /**
   * Lista veículos pendentes de lavagem
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de veículos pendentes
   */
  listarPendentes: async (request, reply) => {
    try {
      // Obter o tipo de usuário para determinar quais veículos mostrar
      const tipoUsuario = request.user.tipo;

      // Verificar se é usuário Master (tipo 0, M ou 1)
      const isMaster = tipoUsuario === '0' || tipoUsuario === 'M' || tipoUsuario === '1';

      // Verificar se foi solicitado um status específico
      const requestedStatus = request.query.status;

      // Definir a condição de filtro com base no tipo de usuário e no status solicitado
      let statusCondition;

      if (isMaster && requestedStatus) {
        // Master solicitou um status específico
        statusCondition = `AND m.StatusLav = '${requestedStatus}'`;
      } else if (isMaster) {
        // Master sem status específico vê veículos pendentes (0) e concluídos (2)
        statusCondition = "AND (m.StatusLav = '0' OR m.StatusLav = '2')";
      } else {
        // Usuário Lavagem vê apenas pendentes (0)
        statusCondition = "AND m.StatusLav = '0'";
      }

      const query = `
        SELECT
          m.Id,
          f.Id AS VeiculoId,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          f.Tipo AS TipoVeiculo,
          c.Nome AS NomeMotorista,
          c.Matricula AS MatriculaMotorista,
          m.DataEntrada,
          m.HoraEntrada,
          m.KmEntrada,
          m.StatusLav,
          m.UsuarioLav,
          u.Usuario AS NomeUsuarioLav
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        INNER JOIN Colaboradores c ON m.MotEntrada = c.Id
        LEFT JOIN Usuarios u ON m.UsuarioLav = u.Id
        WHERE m.DataSaida IS NULL
          ${statusCondition}
        ORDER BY m.DataEntrada ASC, m.HoraEntrada ASC
      `;

      const veiculos = await executeQuery(query);

      // Processar os resultados para formatar campos
      const veiculosProcessados = veiculos.map(v => ({
        id: v.Id,
        veiculoId: v.VeiculoId,
        placa: v.Placa.trim(),
        descricaoVeiculo: v.DescricaoVeiculo ? v.DescricaoVeiculo.trim() : '',
        tipoVeiculo: v.TipoVeiculo ? v.TipoVeiculo.trim() : '',
        nomeMotorista: v.NomeMotorista ? v.NomeMotorista.trim() : '',
        matriculaMotorista: v.MatriculaMotorista ? v.MatriculaMotorista.trim() : '',
        dataEntrada: v.DataEntrada,
        horaEntrada: v.HoraEntrada,
        kmEntrada: v.KmEntrada,
        statusLav: v.StatusLav,
        usuarioLav: v.UsuarioLav,
        nomeUsuarioLav: v.NomeUsuarioLav ? v.NomeUsuarioLav.trim() : '',
        concluido: v.StatusLav === '2' // Flag para indicar se está concluído (aguardando Master)
      }));

      // Obter a data e hora atual do servidor SQL para cálculo correto do tempo no pátio
      // Converter para o fuso horário do Brasil (UTC-3)
      const dataHoraQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        SELECT DATEADD(HOUR, -3, GETUTCDATE()) AS dataHoraAtual
      `;
      const dataHoraResult = await executeQuery(dataHoraQuery);
      const dataHoraAtual = dataHoraResult[0].dataHoraAtual;

      return {
        total: veiculosProcessados.length,
        veiculos: veiculosProcessados,
        dataHoraAtual: dataHoraAtual
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar veículos pendentes de lavagem'
      });
    }
  },

  /**
   * Finaliza uma lavagem (apenas para usuários Master)
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados da lavagem finalizada
   */
  finalizarLavagem: async (request, reply) => {
    const { id } = request.params;

    try {
      // Verificar se o usuário é Master
      const tipoUsuario = request.user.tipo;
      const isMaster = tipoUsuario === '0' || tipoUsuario === 'M' || tipoUsuario === '1';

      if (!isMaster) {
        return reply.code(403).send({
          error: 'Apenas usuários Master podem finalizar lavagens'
        });
      }

      // Verificar se o registro existe e está com status 2 (concluído)
      const checkQuery = `
        SELECT m.Id, m.StatusLav, f.Placa
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        WHERE m.Id = @id
      `;

      const registros = await executeQuery(checkQuery, { id });

      if (registros.length === 0) {
        return reply.code(404).send({
          error: 'Registro não encontrado'
        });
      }

      const registro = registros[0];

      // Verificar se o status é 2 (concluído)
      if (registro.StatusLav !== '2') {
        return reply.code(400).send({
          error: 'Apenas lavagens concluídas podem ser finalizadas'
        });
      }

      // Atualizar registro na MovFrota para finalizar a lavagem
      // Usar o fuso horário do Brasil (UTC-3)
      // Não atualizar o UsuarioLav quando o Master finaliza uma lavagem já concluída (status 2)
      const updateQuery = `
        -- Obter a data e hora atual e converter para o fuso horário do Brasil (UTC-3)
        DECLARE @dataHoraUTC DATETIME = GETUTCDATE();
        DECLARE @dataHoraBrasil DATETIME = DATEADD(HOUR, -3, @dataHoraUTC);
        DECLARE @horaFormatada CHAR(5) = FORMAT(@dataHoraBrasil, 'HH:mm');

        -- Atualizar apenas StatusLav, DataLav e HoraLav, sem modificar UsuarioLav
        UPDATE MovFrota SET
          StatusLav = '1',
          DataLav = @dataHoraBrasil,
          HoraLav = @horaFormatada
        WHERE Id = @id;

        SELECT
          m.Id,
          f.Placa,
          f.Descricao AS DescricaoVeiculo,
          m.DataEntrada,
          m.StatusLav,
          m.DataLav,
          m.HoraLav,
          m.UsuarioLav,
          u.Usuario AS NomeUsuario
        FROM MovFrota m
        INNER JOIN Frota f ON m.Veiculo = f.Id
        LEFT JOIN Usuarios u ON m.UsuarioLav = u.Id
        WHERE m.Id = @id
      `;

      // Não passamos o usuarioId para a query, pois não queremos atualizar o UsuarioLav
      // quando o Master finaliza uma lavagem já concluída
      const result = await executeQuery(updateQuery, {
        id
      });

      if (!result || !result[0]) {
        throw new Error('Falha ao finalizar lavagem');
      }

      return {
        message: 'Lavagem finalizada com sucesso',
        lavagem: {
          id: result[0].Id,
          placa: result[0].Placa.trim(),
          descricaoVeiculo: result[0].DescricaoVeiculo ? result[0].DescricaoVeiculo.trim() : '',
          dataLavagem: result[0].DataLav,
          horaLavagem: result[0].HoraLav,
          usuarioId: result[0].UsuarioLav,
          nomeUsuario: result[0].NomeUsuario ? result[0].NomeUsuario.trim() : ''
        }
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao finalizar lavagem'
      });
    }
  }
};

module.exports = lavagemController;
