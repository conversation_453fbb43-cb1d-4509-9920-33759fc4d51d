# Revisão da Implementação do Controle de Entrada de Cavalos Mecânicos com Carreta

Esta documentação detalha a revisão da implementação da funcionalidade para controlar a entrada e saída de cavalos mecânicos (tipo L) com carretas na portaria, considerando que não é necessário vincular permanentemente o cavalo mecânico à carreta.

## Visão Geral da Nova Abordagem

A implementação revisada permite registrar a entrada de um cavalo mecânico puxando uma carreta, criando registros individuais para cada veículo na tabela `MovFrota`, mas **sem manter uma associação permanente entre eles**. Isso possibilita que um caminhão entre com uma carreta e saia com outra, refletindo melhor a realidade operacional da empresa.

## Alterações Realizadas

### 1. Validação de Placa da Carreta

- Modificada a validação para cavalos mecânicos (tipo "L")
- Agora o sistema exibe um alerta quando a placa da carreta não é informada, mas permite continuar com o cadastro
- Não é mais retornado um erro que impede o cadastro quando a placa da carreta não é informada

### 2. Remoção da Vinculação Permanente

- Removida a utilização da coluna `VeiculoVinculado` para criar uma relação fixa entre o cavalo mecânico e a carreta
- Os registros de entrada são criados de forma independente para cada veículo
- Não é mais atualizado o registro do cavalo mecânico para apontar para a carreta

### 3. Modificações no Controlador da Portaria

#### Registro de Entrada

- Mantida a validação para verificar se um veículo do tipo "L" (cavalo mecânico) possui a placa da carreta informada, mas agora apenas como alerta
- Continua criando dois registros na tabela MovFrota quando necessário (um para o cavalo, outro para a carreta)
- Removida a vinculação entre os dois registros usando o campo `VeiculoVinculado`
- Retorna informações sobre ambos os veículos na resposta, mas sem indicar uma relação permanente entre eles

#### Registro de Saída

- Removida a verificação de veículo vinculado
- Cada veículo (cavalo mecânico e carreta) deve ter sua saída registrada independentemente
- Não é mais necessário verificar o status de ambos os veículos antes de liberar a saída
- Cada veículo tem seu próprio controle de status (abastecimento, vistoria, lavagem)

#### Listagem de Veículos no Pátio

- Removida a busca por carretas vinculadas
- Cada veículo (cavalo mecânico ou carreta) é listado como um registro independente
- Mantida a formatação e processamento dos dados para exibição

## Impacto no Fluxo de Trabalho do Operador

1. Ao registrar entrada de um cavalo mecânico (tipo L), o operador ainda deve informar a placa da carreta, mas o sistema não impedirá o cadastro caso não seja informada
2. O sistema criará registros independentes para ambos os veículos
3. Ao visualizar veículos no pátio, o operador verá todos os veículos como registros independentes
4. Para liberar a saída, os processos (abastecimento, lavagem e vistoria) precisam estar completos apenas para o veículo específico que está saindo
5. Um cavalo mecânico pode entrar com uma carreta e sair com outra, sem restrições do sistema

## Próximos Passos e Considerações

- O frontend precisará ser atualizado para refletir essa nova abordagem, tratando cavalos mecânicos e carretas como entidades independentes
- Relatórios de movimentação de veículos devem ser atualizados para mostrar corretamente os veículos sem vinculação permanente
- Considerar a implementação de uma funcionalidade que permita visualizar o histórico de quais cavalos mecânicos entraram com quais carretas, para fins de análise e auditoria