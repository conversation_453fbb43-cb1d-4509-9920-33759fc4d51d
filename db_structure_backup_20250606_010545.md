# Estrutura do Banco de Dados - BWA Transportes

Este documento descreve a estrutura atual do banco de dados SQL Server utilizado pelo sistema de gestão de frota da BWA Transportes.

**Última atualização:** 06/06/2025, 01:03:37 (extraída automaticamente do banco de dados)

## Tabelas Principais

### 1. MovFrota

Tabela principal que registra a entrada e saída de veículos.

| Campo                 | Tipo          | Descrição                                      |
|-----------------------|---------------|------------------------------------------------|
| Id                    | numeric(18,0) | Identificador único (PK)                       |
| Veiculo               | numeric(18,0) | ID do veículo na tabela Frota (FK)             |
| DataEntrada           | datetime      | Data de entrada                                |
| HoraEntrada           | char(5)       | Hora de entrada                                |
| BafometroEnt          | numeric(18,0) | Status do bafômetro na entrada                 |
| MotEntrada            | numeric(18,0) | ID do motorista na entrada                     |
| KmEntrada             | float         | Quilometragem na entrada                       |
| UsuarioPort           | numeric(18,0) | Usuário que registrou a entrada                |
| Litragem              | float         | Quantidade de combustível                       |
| StatusPosto           | char(2)       | Status de abastecimento (0=NOK, 1=OK)          |
| UsuarioPosto          | numeric(18,0) | Usuário que registrou abastecimento            |
| DataPosto             | datetime      | Data do abastecimento                          |
| HoraPosto             | char(5)       | Hora do abastecimento                          |
| StatusLav             | char(2)       | Status de lavagem (0=NOK, 1=OK)                |
| UsuarioLav            | numeric(18,0) | Usuário que registrou lavagem                  |
| DataLav               | datetime      | Data da lavagem                                |
| HoraLav               | char(5)       | Hora da lavagem                                |
| StatusVist            | char(2)       | Status de vistoria (0=NOK, 1=OK)               |
| UsuarioVist           | numeric(18,0) | Usuário que registrou vistoria                 |
| DataVist              | datetime      | Data da vistoria                               |
| HoraVist              | char(5)       | Hora da vistoria                               |
| KmSaida               | float         | Quilometragem na saída                         |
| DataSaida             | datetime      | Data de saída (NULL se ainda no pátio)         |
| HoraSaida             | char(5)       | Hora de saída                                  |
| UsuarioSaida          | numeric(18,0) | Usuário que registrou a saída                  |
| MotoristaSaida        | numeric(18,0) | ID do motorista na saída                       |
| BafometroSaida        | numeric(18,0) | Status do bafômetro na saída                   |
| StatusOS              | char(2)       | Status de ordem de serviço                     |
| Status                | char(2)       | Status geral do registro                       |
| Encerrante            | numeric(18,0) | Número do encerrante (bomba de combustível)    |
| DataPrev              | datetime      | Data prevista                                  |
| HoraPrev              | char(5)       | Hora prevista                                  |
| DPosto                | char(2)       | Status detalhado do posto                      |
| DLav                  | char(2)       | Status detalhado da lavagem                    |
| DVist                 | char(2)       | Status detalhado da vistoria                   |
| DOSs                  | char(2)       | Status detalhado das OSs                       |
| Destino               | char(60)      | Destino do veículo                             |

**Índices recomendados:**
- PK: Id
- IDX: Veiculo, DataEntrada
- IDX: DataSaida (para filtrar veículos no pátio)

### 2. Usuarios

Tabela de usuários do sistema.

| Campo                 | Tipo          | Descrição                                      |
|-----------------------|---------------|------------------------------------------------|
| Id                    | numeric(18,0) | Identificador único (PK)                       |
| Usuario               | nchar(30)     | Nome de usuário para login                     |
| Senha                 | nchar(6)      | Senha                                          |
| Tipo                  | nchar(1)      | Tipo de usuário (0=master, 2=portaria, 3=posto)|
| Colaborador           | numeric(18,0) | FK para tabela Colaboradores                   |
| Vendedor              | numeric(18,0) | FK para vendedor (não utilizado neste sistema) |
| Email                 | nchar(60)     | Email do usuário                               |
| A01 a A10             | nchar(1)      | Flags de permissões                            |

**Índices recomendados:**
- PK: Id
- UNIQUE: Usuario
- IDX: Tipo

### 3. Frota

Cadastro de veículos.

| Campo                 | Tipo          | Descrição                                      |
|-----------------------|---------------|------------------------------------------------|
| Id                    | numeric(18,0) | Identificador único (PK)                       |
| Placa                 | char(7)       | Placa do veículo                               |
| Descricao             | char(50)      | Descrição do veículo                           |
| Tipo                  | char(1)       | Tipo de veículo (1=Cavalo, 3=Cavalo Truck, 4=Carreta) |
| Ano                   | char(4)       | Ano do veículo                                 |
| Aquisicao             | datetime      | Data de aquisição                              |
| Observacoes           | text          | Observações                                    |
| Usuario               | numeric(18,0) | Usuário que cadastrou                          |
| UsuarioData           | datetime      | Data de cadastro                               |
| UsuarioHora           | char(5)       | Hora de cadastro                               |

**Índices recomendados:**
- PK: Id
- UNIQUE: Placa
- IDX: Tipo

### 4. CheckList

Itens que compõem o checklist de vistoria.

| Campo                 | Tipo          | Descrição                                      |
|-----------------------|---------------|------------------------------------------------|
| Id                    | numeric(18,0) | Identificador único (PK)                       |
| Tipo                  | char(1)       | Tipo de veículo para vistoria                  |
| Descricao             | char(120)     | Descrição do item de checklist                 |

**Índices recomendados:**
- PK: Id
- IDX: Tipo

### 5. OS

Ordens de serviço para manutenção.

| Campo                 | Tipo          | Descrição                                      |
|-----------------------|---------------|------------------------------------------------|
| Id                    | numeric(18,0) | Identificador único                            |
| Descricao             | char(120)     | Descrição do problema                          |
| Abertura              | datetime      | Data de abertura                               |
| AberHora              | char(5)       | Hora de abertura                               |
| ColAceite             | numeric(18,0) | Colaborador que aceitou a OS                   |
| AceiteData            | datetime      | Data do aceite                                 |
| AceiteHora            | char(5)       | Hora do aceite                                 |
| Status                | char(1)       | Status (1=aguardando, 2=execução, 3=peça)      |
| TerminoData           | datetime      | Data de conclusão                              |
| TerminoHora           | char(5)       | Hora de conclusão                              |
| Observacoes           | text          | Observações                                    |
| Usuario               | numeric(18,0) | Usuário que registrou                          |
| UsuarioData           | datetime      | Data de registro                               |
| UsuarioHora           | char(5)       | Hora de registro                               |
| MovFrota              | numeric(18,0) | ID do registro na tabela MovFrota              |
| ItemCheckList         | numeric(18,0) | ID do item de checklist relacionado            |
| VeiculoFrota          | numeric(18,0) | ID do veículo na tabela Frota                  |
| DataPrev              | datetime      | Data prevista para conclusão                   |
| HoraPrev              | char(5)       | Hora prevista para conclusão                   |
| TempoServ             | char(5)       | Tempo de serviço                               |

**Observação:** A tabela OS não possui chave primária definida no banco de dados. O campo Id é identity mas não é PK.

**Índices recomendados:**
- IDX: Status, Abertura

### 6. Colaboradores

Cadastro de funcionários/motoristas.

| Campo                 | Tipo          | Descrição                                      |
|-----------------------|---------------|------------------------------------------------|
| Id                    | numeric(18,0) | Identificador único (PK)                       |
| Matricula             | char(10)      | Matrícula do colaborador                       |
| Nome                  | char(50)      | Nome completo                                  |
| Funcao                | char(50)      | Função/cargo                                   |
| Local                 | char(50)      | Local de trabalho                              |
| Nascimento            | datetime      | Data de nascimento                             |
| Sexo                  | char(10)      | Sexo                                           |
| RG                    | char(20)      | RG                                             |
| CPF                   | char(20)      | CPF                                            |
| Endereco              | char(100)     | Endereço                                       |
| Bairro                | char(20)      | Bairro                                         |
| Cidade                | char(20)      | Cidade                                         |
| UF                    | char(2)       | Estado                                         |
| CEP                   | char(9)       | CEP                                            |
| Celular               | char(20)      | Telefone celular                               |
| TelRes                | char(20)      | Telefone residencial                           |
| TelRecado             | char(20)      | Telefone para recado                           |
| Email                 | char(100)     | Email                                          |
| Salvatagem            | char(3)       | Indicador de salvatagem                        |
| TreinamentoPT         | char(3)       | Indicador de treinamento PT                    |
| TreinamentoRewt       | char(3)       | Indicador de treinamento Rewt                  |
| PIS                   | char(15)      | Número do PIS                                  |
| CTPS                  | char(20)      | Número da CTPS                                 |
| Cadastro              | datetime      | Data de cadastro                               |
| Admissao              | datetime      | Data de admissão                               |
| Desligamento          | datetime      | Data de desligamento                           |
| Observacoes           | text          | Observações                                    |
| Usuario               | numeric(18,0) | Usuário que cadastrou                          |
| UsuarioData           | datetime      | Data de cadastro                               |
| UsuarioHora           | char(5)       | Hora de cadastro                               |

**Índices recomendados:**
- PK: Id
- UNIQUE: Matricula
- IDX: Nome

## Relacionamentos

1. **MovFrota** é a tabela central que registra todos os processos de movimentação dos veículos
2. **Frota** contém o cadastro dos veículos que serão movimentados
3. **Colaboradores** armazena informações dos motoristas e demais funcionários
4. **Usuarios** contém os usuários do sistema com seus respectivos perfis de acesso
5. **CheckList** define os itens de verificação por tipo de veículo
6. **OS** armazena as ordens de serviço geradas a partir de problemas detectados nas vistorias

## Alterações Recentes

1. **Adição da coluna Encerrante na tabela MovFrota**
   - Tipo: numeric(18,0)
   - Descrição: Armazena o número do encerrante (bomba de combustível) no abastecimento

2. **Adição das colunas StatusOS e Status na tabela MovFrota**
   - Tipo: char(2)
   - Descrição: Controle de status de OS e status geral do registro

3. **Adição das colunas DataPrev e HoraPrev na tabela OS**
   - Tipo: datetime e char(5)
   - Descrição: Data e hora previstas para conclusão da OS

4. **Alteração nos campos de status**
   - Os campos StatusPosto, StatusLav e StatusVist foram alterados de char(1) para char(2)
   - Permite maior flexibilidade nos status

5. **Novas colunas de status detalhado na tabela MovFrota**
   - DPosto, DLav, DVist, DOSs: char(2) cada
   - Descrição: Status detalhados para cada etapa do processo

6. **Adição da coluna Destino na tabela MovFrota**
   - Tipo: char(60)
   - Descrição: Campo para registrar o destino do veículo

## Observações Importantes

1. Os campos de status (StatusPosto, StatusLav, StatusVist) são do tipo char(2) e usam "0" para pendente e "1" para finalizado
2. O campo Tipo na tabela Usuarios é nchar(1) e usa "0" para master, "2" para portaria, "3" para posto, "4" para vistoria, "5" para oficina e "6" para lavagem
3. Os status de OS são "1" (aguardando aceite), "2" (em execução), "3" (aguardando peça), "4" (finalizada), "5" (pendente) e "6" (concluída)
4. Na tabela CheckList, o campo Tipo define para qual tipo de veículo o item se aplica
5. Veículos só podem sair quando todos os status estão como "1" (OK)
6. O campo ItemCheckList na tabela OS é referenciado como "ItemChecklist" (com "l" minúsculo) em algumas partes do código, mas está definido como "ItemCheckList" (com "L" maiúsculo) no banco de dados
7. Tipos de veículos na tabela Frota:
   - Tipo 1: Cavalo (veículo trator que pode puxar carretas)
   - Tipo 3: Cavalo Truck (conjunto único que não se separa)
   - Tipo 4: Carreta (reboque que precisa de um cavalo para ser movimentado)
8. Regras especiais por tipo de veículo:
   - Cavalos (tipo 1) e Carretas (tipo 4) devem ter registros separados na tabela MovFrota
   - Carretas (tipo 4) não precisam passar pelo abastecimento (não aparecem na lista do Posto)
   - Cavalo Truck (tipo 3) é tratado como um único veículo
   - Uma carreta pode entrar com um cavalo e sair com outro
9. Na vistoria, independente do resultado (OK ou NOK), o status da vistoria será válido (1)
10. Se houver itens NOK na vistoria, será criada uma OS para cada item
11. Veículos com OS pendentes (não concluídas) não podem sair

## Configuração de Conexão

Para conectar ao banco de dados, utilize as seguintes configurações:

```
Servidor: **************
Porta: 1433
Banco de dados: BWA
Usuário: sa
Senha: !bwa001
```

Essas configurações devem ser definidas no arquivo `.env` do backend, conforme o exemplo abaixo:

```
DB_SERVER=**************
DB_PORT=1433
DB_NAME=BWA
DB_USER=sa
DB_PASSWORD=!bwa001
DB_ENCRYPT=true
DB_TRUST_SERVER_CERTIFICATE=true
```