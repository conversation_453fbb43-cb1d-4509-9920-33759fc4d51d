const sql = require('mssql');
require('dotenv').config();

// Configuração de conexão com o SQL Server
const config = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  port: parseInt(process.env.DB_PORT) || 1433,
  database: process.env.DB_NAME,
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_SERVER_CERTIFICATE === 'true',
    enableArithAbort: true,
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 30000,
    requestTimeout: parseInt(process.env.DB_REQUEST_TIMEOUT) || 30000
  },
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 10,
    min: parseInt(process.env.DB_POOL_MIN) || 0,
    idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT) || 30000
  }
};

// Log da configuração (sem a senha)
const configLog = { ...config, password: '******' };
console.log('Configuração de conexão com o banco de dados:', configLog);

// Função para conectar ao banco de dados
const connectDB = async () => {
  try {
    const pool = await sql.connect(config);
    console.log('Conectado ao SQL Server');
    return pool;
  } catch (err) {
    console.error('Erro ao conectar ao SQL Server:', err);
    throw err;
  }
};

// Função para executar queries com parâmetros nomeados
const executeQuery = async (query, params = {}) => {
  try {
    const pool = await connectDB();
    const request = pool.request();

    // Adicionar parâmetros à requisição
    Object.keys(params).forEach(key => {
      // Determinar o tipo de dado do parâmetro
      const value = params[key];
      if (value === null || value === undefined) {
        request.input(key, sql.NVarChar, null);
      } else if (typeof value === 'number') {
        if (Number.isInteger(value)) {
          request.input(key, sql.Int, value);
        } else {
          request.input(key, sql.Float, value);
        }
      } else if (typeof value === 'boolean') {
        request.input(key, sql.Bit, value);
      } else if (value instanceof Date) {
        request.input(key, sql.DateTime, value);
      } else {
        request.input(key, sql.NVarChar, value.toString());
      }
    });

    const result = await request.query(query);
    return result.recordset;
  } catch (err) {
    console.error('Erro ao executar query:', err);
    throw err;
  }
};

// Função para executar stored procedures
const executeStoredProcedure = async (procedureName, params = {}) => {
  try {
    const pool = await connectDB();
    const request = pool.request();

    // Adicionar parâmetros à requisição
    Object.keys(params).forEach(key => {
      const value = params[key];
      if (value === null || value === undefined) {
        request.input(key, sql.NVarChar, null);
      } else if (typeof value === 'number') {
        if (Number.isInteger(value)) {
          request.input(key, sql.Int, value);
        } else {
          request.input(key, sql.Float, value);
        }
      } else if (typeof value === 'boolean') {
        request.input(key, sql.Bit, value);
      } else if (value instanceof Date) {
        request.input(key, sql.DateTime, value);
      } else {
        request.input(key, sql.NVarChar, value.toString());
      }
    });

    const result = await request.execute(procedureName);
    return result.recordset;
  } catch (err) {
    console.error(`Erro ao executar stored procedure ${procedureName}:`, err);
    throw err;
  }
};

// Helper para executar uma query paginada
const executePagedQuery = async (baseQuery, countQuery, params = {}, page = 1, pageSize = 10) => {
  try {
    const offset = (page - 1) * pageSize;

    // Adicionar paginação à query base
    const pagedQuery = `${baseQuery}
      ORDER BY Id OFFSET ${offset} ROWS FETCH NEXT ${pageSize} ROWS ONLY`;

    // Executar a query paginada e a query de contagem
    const pool = await connectDB();

    const request1 = pool.request();
    const request2 = pool.request();

    // Adicionar parâmetros às requisições
    Object.keys(params).forEach(key => {
      const value = params[key];
      if (value === null || value === undefined) {
        request1.input(key, sql.NVarChar, null);
        request2.input(key, sql.NVarChar, null);
      } else if (typeof value === 'number') {
        if (Number.isInteger(value)) {
          request1.input(key, sql.Int, value);
          request2.input(key, sql.Int, value);
        } else {
          request1.input(key, sql.Float, value);
          request2.input(key, sql.Float, value);
        }
      } else if (typeof value === 'boolean') {
        request1.input(key, sql.Bit, value);
        request2.input(key, sql.Bit, value);
      } else if (value instanceof Date) {
        request1.input(key, sql.DateTime, value);
        request2.input(key, sql.DateTime, value);
      } else {
        request1.input(key, sql.NVarChar, value.toString());
        request2.input(key, sql.NVarChar, value.toString());
      }
    });

    const recordsPromise = request1.query(pagedQuery);
    const countPromise = request2.query(countQuery);

    const [recordsResult, countResult] = await Promise.all([recordsPromise, countPromise]);

    const total = countResult.recordset[0].total;
    const totalPages = Math.ceil(total / pageSize);

    return {
      data: recordsResult.recordset,
      pagination: {
        total,
        page,
        pageSize,
        totalPages
      }
    };
  } catch (err) {
    console.error('Erro ao executar query paginada:', err);
    throw err;
  }
};

// Classe para transações
class Transaction {
  constructor(transaction) {
    this.transaction = transaction;
  }

  async executeQuery(query, params = {}) {
    try {
      const request = this.transaction.request();

      // Adicionar parâmetros à requisição
      Object.keys(params).forEach(key => {
        const value = params[key];
        if (value === null || value === undefined) {
          request.input(key, sql.NVarChar, null);
        } else if (typeof value === 'number') {
          if (Number.isInteger(value)) {
            request.input(key, sql.Int, value);
          } else {
            request.input(key, sql.Float, value);
          }
        } else if (typeof value === 'boolean') {
          request.input(key, sql.Bit, value);
        } else if (value instanceof Date) {
          request.input(key, sql.DateTime, value);
        } else {
          request.input(key, sql.NVarChar, value.toString());
        }
      });

      const result = await request.query(query);
      return result.recordset;
    } catch (err) {
      console.error('Erro ao executar query na transação:', err);
      throw err;
    }
  }

  async commit() {
    try {
      await this.transaction.commit();
    } catch (err) {
      console.error('Erro ao fazer commit da transação:', err);
      throw err;
    }
  }

  async rollback() {
    try {
      await this.transaction.rollback();
    } catch (err) {
      console.error('Erro ao fazer rollback da transação:', err);
      throw err;
    }
  }
}

// Função para iniciar uma transação
const beginTransaction = async () => {
  try {
    const pool = await connectDB();
    const transaction = new sql.Transaction(pool);
    await transaction.begin();
    return new Transaction(transaction);
  } catch (err) {
    console.error('Erro ao iniciar transação:', err);
    throw err;
  }
};

module.exports = {
  connectDB,
  executeQuery,
  executeStoredProcedure,
  executePagedQuery,
  beginTransaction,
  sql
};