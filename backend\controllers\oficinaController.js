const { executeQuery } = require('../models/db');

/**
 * Controlador de Oficina
 * Gerencia ordens de serviço e manutenções
 */
const oficinaController = {
  /**
   * Lista ordens de serviço
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Array} Lista de ordens de serviço
   */
  listarOrdens: async (request, reply) => {
    const { status } = request.query;

    // Log para depuração
    request.log.info(`Listando ordens de serviço. Status: ${status || 'todos'}`);

    try {
      let query = `
        SELECT
          o.Id,
          o.<PERSON>,
          o.<PERSON>,
          o.<PERSON>,
          o.ColAceite,
          o.Aceite<PERSON>ata,
          o.<PERSON>ite<PERSON>ora,
          o.Status,
          o.TerminoData,
          o.Termino<PERSON>ora,
          o.DataPrev,
          o.<PERSON>,
          o.<PERSON>,
          o.<PERSON>,
          o.<PERSON>,
          o<PERSON>,
          o<PERSON>,
          o.<PERSON>heck<PERSON>,
          c.Nome AS NomeColaborador,
          c.Matricula AS MatriculaColaborador,
          u.Usuario AS NomeUsuario,
          f.Placa AS PlacaVeiculo,
          f.Descricao AS DescricaoVeiculo,
          cl.Descricao AS ItemChecklistDescricao
        FROM OS o
        LEFT JOIN Colaboradores c ON o.ColAceite = c.Id
        LEFT JOIN Usuarios u ON o.Usuario = u.Id
        LEFT JOIN Frota f ON o.VeiculoFrota = f.Id
        LEFT JOIN CheckList cl ON o.ItemChecklist = cl.Id
        WHERE o.Status <> '4' -- Excluir OS com status 4 (Finalizada) para todos os usuários
      `;

      // Filtrar por status se fornecido
      if (status) {
        query += ` AND o.Status = @status`;
      }

      query += ` ORDER BY o.Abertura DESC, o.AberHora DESC`;

      const ordens = await executeQuery(query, { status });

      // Processar os resultados para formatar campos
      const ordensProcessadas = ordens.map(o => {
        const ordem = {
          id: o.Id,
          descricao: o.Descricao ? o.Descricao.trim() : '',
          dataAbertura: o.Abertura,
          horaAbertura: o.AberHora ? o.AberHora.trim() : '',
          colaboradorId: o.ColAceite,
          nomeColaborador: o.NomeColaborador ? o.NomeColaborador.trim() : '',
          matriculaColaborador: o.MatriculaColaborador ? o.MatriculaColaborador.trim() : '',
          dataAceite: o.AceiteData,
          horaAceite: o.AceiteHora ? o.AceiteHora.trim() : '',
          status: o.Status ? o.Status.trim() : '',
          statusDescricao: getStatusDescricao(o.Status),
          dataTermino: o.TerminoData,
          horaTermino: o.TerminoHora ? o.TerminoHora.trim() : '',
          dataPrevista: o.DataPrev,
          horaPrevista: o.HoraPrev ? o.HoraPrev.trim() : '',
          tempoServico: o.TempoServ ? o.TempoServ.trim() : '',
          observacoes: o.Observacoes || '',
          usuarioId: o.Usuario,
          nomeUsuario: o.NomeUsuario ? o.NomeUsuario.trim() : '',
          itemChecklistId: o.ItemChecklist,
          itemChecklistDescricao: o.ItemChecklistDescricao ? o.ItemChecklistDescricao.trim() : ''
        };

        // Adicionar informações do veículo se disponíveis
        if (o.VeiculoFrota && o.PlacaVeiculo) {
          ordem.veiculo = {
            id: o.VeiculoFrota,
            placa: o.PlacaVeiculo.trim(),
            descricao: o.DescricaoVeiculo ? o.DescricaoVeiculo.trim() : ''
          };
        }

        // Adicionar ID do MovFrota se disponível
        if (o.MovFrota) {
          ordem.movFrotaId = o.MovFrota;
        }

        return ordem;
      });

      // Log para depuração
      request.log.info(`Encontradas ${ordensProcessadas.length} ordens de serviço`);

      const resultado = {
        total: ordensProcessadas.length,
        ordens: ordensProcessadas
      };

      return resultado;
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao listar ordens de serviço'
      });
    }
  },

  /**
   * Busca uma ordem de serviço específica
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados da ordem de serviço
   */
  buscarOrdem: async (request, reply) => {
    const { id } = request.params;

    try {
      const query = `
        SELECT
          o.Id,
          o.Descricao,
          o.Abertura,
          o.AberHora,
          o.ColAceite,
          o.AceiteData,
          o.AceiteHora,
          o.Status,
          o.TerminoData,
          o.TerminoHora,
          o.DataPrev,
          o.HoraPrev,
          o.TempoServ,
          o.Observacoes,
          o.Usuario,
          o.VeiculoFrota,
          o.MovFrota,
          o.ItemChecklist,
          c.Nome AS NomeColaborador,
          c.Matricula AS MatriculaColaborador,
          u.Usuario AS NomeUsuario,
          f.Placa AS PlacaVeiculo,
          f.Descricao AS DescricaoVeiculo,
          cl.Descricao AS ItemChecklistDescricao
        FROM OS o
        LEFT JOIN Colaboradores c ON o.ColAceite = c.Id
        LEFT JOIN Usuarios u ON o.Usuario = u.Id
        LEFT JOIN Frota f ON o.VeiculoFrota = f.Id
        LEFT JOIN CheckList cl ON o.ItemChecklist = cl.Id
        WHERE o.Id = @id
      `;

      const ordens = await executeQuery(query, { id });

      if (ordens.length === 0) {
        return reply.code(404).send({
          error: 'Ordem de serviço não encontrada'
        });
      }

      const ordem = ordens[0];

      // Log para depuração
      console.log('Dados da ordem do banco:', ordem);

      // Formatar os campos
      const ordemFormatada = {
        id: ordem.Id,
        descricao: ordem.Descricao ? ordem.Descricao.trim() : '',
        dataAbertura: ordem.Abertura,
        horaAbertura: ordem.AberHora ? ordem.AberHora.trim() : '',
        colaboradorId: ordem.ColAceite,
        nomeColaborador: ordem.NomeColaborador ? ordem.NomeColaborador.trim() : '',
        matriculaColaborador: ordem.MatriculaColaborador ? ordem.MatriculaColaborador.trim() : '',
        dataAceite: ordem.AceiteData,
        horaAceite: ordem.AceiteHora ? ordem.AceiteHora.trim() : '',
        status: ordem.Status ? ordem.Status.trim() : '',
        statusDescricao: getStatusDescricao(ordem.Status),
        dataTermino: ordem.TerminoData,
        horaTermino: ordem.TerminoHora ? ordem.TerminoHora.trim() : '',
        dataPrevista: ordem.DataPrev,
        horaPrevista: ordem.HoraPrev ? ordem.HoraPrev.trim() : '',
        tempoServico: ordem.TempoServ ? ordem.TempoServ.trim() : '',
        observacoes: ordem.Observacoes || '',
        usuarioId: ordem.Usuario,
        nomeUsuario: ordem.NomeUsuario ? ordem.NomeUsuario.trim() : '',
        itemChecklistId: ordem.ItemChecklist,
        itemChecklistDescricao: ordem.ItemChecklistDescricao ? ordem.ItemChecklistDescricao.trim() : ''
      };

      // Adicionar informações do veículo se disponíveis
      if (ordem.VeiculoFrota && ordem.PlacaVeiculo) {
        ordemFormatada.veiculo = {
          id: ordem.VeiculoFrota,
          placa: ordem.PlacaVeiculo.trim(),
          descricao: ordem.DescricaoVeiculo ? ordem.DescricaoVeiculo.trim() : ''
        };
      }

      // Adicionar ID do MovFrota se disponível
      if (ordem.MovFrota) {
        ordemFormatada.movFrotaId = ordem.MovFrota;
      }

      // Log para depuração
      console.log('Ordem formatada para retorno:', ordemFormatada);

      return {
        ordem: ordemFormatada
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao buscar ordem de serviço'
      });
    }
  },

  /**
   * Atualiza o status de uma ordem de serviço
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados da ordem atualizada
   */
  atualizarStatus: async (request, reply) => {
    const { id } = request.params;
    // Usar let em vez de const para permitir modificar o status
    const { observacao } = request.body;
    let { status } = request.body;

    try {
      // Verificar se a ordem existe
      const checkQuery = `SELECT * FROM OS WHERE Id = @id`;
      const ordens = await executeQuery(checkQuery, { id });

      if (ordens.length === 0) {
        return reply.code(404).send({
          error: 'Ordem de serviço não encontrada'
        });
      }

      const ordem = ordens[0];

      // Validar o status
      if (status !== '1' && status !== '2' && status !== '3' && status !== '4' && status !== '5' && status !== '6' && status !== 'concluida') {
        return reply.code(400).send({
          error: 'Status inválido. Use 1=Aguardando aceite, 2=Em execução, 3=Aguardando peça, 4=Finalizada, 5=Pendente, 6=Concluída'
        });
      }

      // Verificar se a OS já está concluída (status 6) ou finalizada (status 4)
      // e impedir a alteração de status, exceto para usuários Master que podem finalizar uma OS concluída
      if ((ordem.Status === '6' || ordem.Status === '4') &&
          !(isMaster && ordem.Status === '6' && status === '4')) {
        return reply.code(400).send({
          error: 'Não é possível alterar o status de uma OS que já foi concluída ou finalizada.'
        });
      }

      // Obter o ID do usuário atual a partir do token JWT
      const usuarioId = request.user.id;

      // Obter o tipo de usuário para determinar o status
      const tipoUsuario = request.user.tipo;

      // Verificar se é usuário Master (tipo 0, M ou 1)
      const isMaster = tipoUsuario === '0' || tipoUsuario === 'M' || tipoUsuario === '1';

      // Se o usuário não for Master e estiver tentando finalizar (status 4 ou concluida),
      // alterar para status 6 (Concluída, aguardando finalização do Master)
      if (!isMaster && (status === '4' || status === 'concluida')) {
        status = '6';
      }

      // Verificar se uma nova observação foi fornecida
      const novaObservacao = request.body.observacao || observacao;

      // Preparar campos para atualização
      // Usar o fuso horário do Brasil (UTC-3)
      let updateFields = `
        Status = @status,
        Usuario = @usuarioId,
        UsuarioData = DATEADD(HOUR, -3, GETUTCDATE()),
        UsuarioHora = FORMAT(DATEADD(HOUR, -3, GETUTCDATE()), 'HH:mm')
      `;

      // Atualizar observações apenas se uma nova observação foi fornecida
      if (novaObservacao && novaObservacao.trim() !== '') {
        updateFields += `,
          Observacoes = @observacoesCombinadas
        `;
      }

      // Se estiver aceitando a OS (status 2), preencher dados de aceite
      if (status === '2' && ordem.Status === '1') {
        updateFields += `,
          ColAceite = @colaboradorId,
          AceiteData = DATEADD(HOUR, -3, GETUTCDATE()),
          AceiteHora = FORMAT(DATEADD(HOUR, -3, GETUTCDATE()), 'HH:mm')
        `;
      }

      // Se estiver finalizando a OS, preencher data de término e outros campos necessários
      if ((ordem.Status !== '4' && ordem.Status !== '6') && (status === '4' || status === '6' || status === 'concluida')) {
        // Adicionar data e hora de término apenas se for Master finalizando (status 4)
        // Para usuários regulares (status 6), não atualizar data/hora
        if (isMaster && status === '4') {
          updateFields += `,
            TerminoData = DATEADD(HOUR, -3, GETUTCDATE()),
            TerminoHora = FORMAT(DATEADD(HOUR, -3, GETUTCDATE()), 'HH:mm')
          `;
        }

        // Garantir que o status seja correto com base no tipo de usuário
        if (status === 'concluida') {
          status = isMaster ? '4' : '6';
        }

        // Não adicionar observação automática de finalização
      }

      // Atualizar status
      const updateQuery = `
        UPDATE OS SET
          ${updateFields}
        WHERE Id = @id;

        SELECT
          o.Id,
          o.Descricao,
          o.Abertura,
          o.AberHora,
          o.ColAceite,
          o.AceiteData,
          o.AceiteHora,
          o.Status,
          o.TerminoData,
          o.TerminoHora,
          o.DataPrev,
          o.HoraPrev,
          o.TempoServ,
          o.Observacoes,
          o.Usuario,
          o.VeiculoFrota,
          o.MovFrota,
          o.ItemChecklist,
          c.Nome AS NomeColaborador,
          c.Matricula AS MatriculaColaborador,
          u.Usuario AS NomeUsuario,
          f.Placa AS PlacaVeiculo,
          f.Descricao AS DescricaoVeiculo,
          cl.Descricao AS ItemChecklistDescricao
        FROM OS o
        LEFT JOIN Colaboradores c ON o.ColAceite = c.Id
        LEFT JOIN Usuarios u ON o.Usuario = u.Id
        LEFT JOIN Frota f ON o.VeiculoFrota = f.Id
        LEFT JOIN CheckList cl ON o.ItemChecklist = cl.Id
        WHERE o.Id = @id;
      `;

      const colaboradorId = request.body.colaborador_id || null;

      // Preparar os parâmetros para a consulta
      const queryParams = {
        id,
        status,
        usuarioId,
        colaboradorId
      };

      // Adicionar o parâmetro de observações combinadas se necessário
      if (novaObservacao && novaObservacao.trim() !== '') {
        const observacoesAnteriores = ordem.Observacoes || '';
        const observacoesCombinadas = observacoesAnteriores ? `${novaObservacao}\n${observacoesAnteriores}` : novaObservacao;
        queryParams.observacoesCombinadas = observacoesCombinadas;
      }

      const result = await executeQuery(updateQuery, queryParams);

      if (!result || !result[0]) {
        throw new Error('Falha ao atualizar ordem de serviço');
      }

      const ordemAtualizada = result[0];

      // Se a ordem foi finalizada pelo Master (status 4), atualizar o status do veículo na MovFrota
      if (status === '4' && isMaster && (ordem.Status !== '4')) {
        try {
          // Verificar se a OS tem um registro MovFrota associado
          if (ordem.MovFrota) {
            // Atualizar o registro específico do MovFrota
            const updateMovFrotaQuery = `
              UPDATE m SET
                StatusVist = '1' -- Marcar vistoria como OK
              FROM MovFrota m
              WHERE m.Id = @movFrotaId
                AND m.DataSaida IS NULL
                AND (m.StatusVist = '0' OR m.StatusVist = '2')
            `;

            await executeQuery(updateMovFrotaQuery, { movFrotaId: ordem.MovFrota });
          } else {
            // Buscar registros na MovFrota que precisam de atualização (fallback)
            const updateMovFrotaQuery = `
              UPDATE m SET
                StatusVist = '1' -- Marcar vistoria como OK
              FROM MovFrota m
              INNER JOIN Frota f ON m.Veiculo = f.Id
              WHERE m.DataSaida IS NULL
                AND (m.StatusVist = '0' OR m.StatusVist = '2')
                AND GETDATE() BETWEEN DATEADD(day, -3, GETDATE()) AND GETDATE()
            `;

            await executeQuery(updateMovFrotaQuery);
          }
        } catch (updateError) {
          request.log.error('Erro ao atualizar MovFrota:', updateError);
          // Não falhar a operação principal se esta atualização falhar
        }
      }

      // Formatar os campos
      const ordemFormatada = {
        id: ordemAtualizada.Id,
        descricao: ordemAtualizada.Descricao ? ordemAtualizada.Descricao.trim() : '',
        dataAbertura: ordemAtualizada.Abertura,
        horaAbertura: ordemAtualizada.AberHora ? ordemAtualizada.AberHora.trim() : '',
        colaboradorId: ordemAtualizada.ColAceite,
        nomeColaborador: ordemAtualizada.NomeColaborador ? ordemAtualizada.NomeColaborador.trim() : '',
        matriculaColaborador: ordemAtualizada.MatriculaColaborador ? ordemAtualizada.MatriculaColaborador.trim() : '',
        dataAceite: ordemAtualizada.AceiteData,
        horaAceite: ordemAtualizada.AceiteHora ? ordemAtualizada.AceiteHora.trim() : '',
        status: ordemAtualizada.Status ? ordemAtualizada.Status.trim() : '',
        statusDescricao: getStatusDescricao(ordemAtualizada.Status),
        dataTermino: ordemAtualizada.TerminoData,
        horaTermino: ordemAtualizada.TerminoHora ? ordemAtualizada.TerminoHora.trim() : '',
        dataPrevista: ordemAtualizada.DataPrev,
        horaPrevista: ordemAtualizada.HoraPrev ? ordemAtualizada.HoraPrev.trim() : '',
        tempoServico: ordemAtualizada.TempoServ ? ordemAtualizada.TempoServ.trim() : '',
        observacoes: ordemAtualizada.Observacoes || '',
        usuarioId: ordemAtualizada.Usuario,
        nomeUsuario: ordemAtualizada.NomeUsuario ? ordemAtualizada.NomeUsuario.trim() : '',
        itemChecklistId: ordemAtualizada.ItemChecklist,
        itemChecklistDescricao: ordemAtualizada.ItemChecklistDescricao ? ordemAtualizada.ItemChecklistDescricao.trim() : ''
      };

      // Adicionar informações do veículo se disponíveis
      if (ordemAtualizada.VeiculoFrota && ordemAtualizada.PlacaVeiculo) {
        ordemFormatada.veiculo = {
          id: ordemAtualizada.VeiculoFrota,
          placa: ordemAtualizada.PlacaVeiculo.trim(),
          descricao: ordemAtualizada.DescricaoVeiculo ? ordemAtualizada.DescricaoVeiculo.trim() : ''
        };
      }

      // Adicionar ID do MovFrota se disponível
      if (ordemAtualizada.MovFrota) {
        ordemFormatada.movFrotaId = ordemAtualizada.MovFrota;
      }

      return {
        message: 'Status da ordem atualizado com sucesso',
        ordem: ordemFormatada
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao atualizar status da ordem'
      });
    }
  },

  /**
   * Adiciona uma observação a uma ordem de serviço
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados da ordem atualizada
   */
  adicionarObservacao: async (request, reply) => {
    const { id } = request.params;
    const { observacao } = request.body;

    if (!observacao || observacao.trim() === '') {
      return reply.code(400).send({
        error: 'A observação não pode estar vazia'
      });
    }

    try {
      // Verificar se a ordem existe
      const checkQuery = `SELECT * FROM OS WHERE Id = @id`;
      const ordens = await executeQuery(checkQuery, { id });

      if (ordens.length === 0) {
        return reply.code(404).send({
          error: 'Ordem de serviço não encontrada'
        });
      }

      const ordem = ordens[0];

      // Obter o ID do usuário atual a partir do token JWT
      const usuarioId = request.user.id;

      // Usar a observação diretamente sem adicionar timestamp
      // Concatenar a nova observação com as existentes
      const observacoesAnteriores = ordem.Observacoes || '';
      const novaObservacao = observacoesAnteriores ? `${observacao}\n${observacoesAnteriores}` : observacao;

      // Atualizar observações
      const updateQuery = `
        UPDATE OS SET
          Observacoes = @novaObservacao,
          Usuario = @usuarioId,
          UsuarioData = DATEADD(HOUR, -3, GETUTCDATE()),
          UsuarioHora = FORMAT(DATEADD(HOUR, -3, GETUTCDATE()), 'HH:mm')
        WHERE Id = @id;

        SELECT
          o.Id,
          o.Descricao,
          o.Abertura,
          o.AberHora,
          o.ColAceite,
          o.AceiteData,
          o.AceiteHora,
          o.Status,
          o.TerminoData,
          o.TerminoHora,
          o.DataPrev,
          o.HoraPrev,
          o.TempoServ,
          o.Observacoes,
          o.Usuario,
          o.VeiculoFrota,
          o.MovFrota,
          o.ItemChecklist,
          c.Nome AS NomeColaborador,
          c.Matricula AS MatriculaColaborador,
          u.Usuario AS NomeUsuario,
          f.Placa AS PlacaVeiculo,
          f.Descricao AS DescricaoVeiculo,
          cl.Descricao AS ItemChecklistDescricao
        FROM OS o
        LEFT JOIN Colaboradores c ON o.ColAceite = c.Id
        LEFT JOIN Usuarios u ON o.Usuario = u.Id
        LEFT JOIN Frota f ON o.VeiculoFrota = f.Id
        LEFT JOIN CheckList cl ON o.ItemChecklist = cl.Id
        WHERE o.Id = @id;
      `;

      const result = await executeQuery(updateQuery, {
        id,
        novaObservacao,
        usuarioId
      });

      if (!result || !result[0]) {
        throw new Error('Falha ao atualizar observações');
      }

      const ordemAtualizada = result[0];

      // Formatar os campos
      const ordemFormatada = {
        id: ordemAtualizada.Id,
        descricao: ordemAtualizada.Descricao ? ordemAtualizada.Descricao.trim() : '',
        dataAbertura: ordemAtualizada.Abertura,
        horaAbertura: ordemAtualizada.AberHora ? ordemAtualizada.AberHora.trim() : '',
        colaboradorId: ordemAtualizada.ColAceite,
        nomeColaborador: ordemAtualizada.NomeColaborador ? ordemAtualizada.NomeColaborador.trim() : '',
        matriculaColaborador: ordemAtualizada.MatriculaColaborador ? ordemAtualizada.MatriculaColaborador.trim() : '',
        dataAceite: ordemAtualizada.AceiteData,
        horaAceite: ordemAtualizada.AceiteHora ? ordemAtualizada.AceiteHora.trim() : '',
        status: ordemAtualizada.Status ? ordemAtualizada.Status.trim() : '',
        statusDescricao: getStatusDescricao(ordemAtualizada.Status),
        dataTermino: ordemAtualizada.TerminoData,
        horaTermino: ordemAtualizada.TerminoHora ? ordemAtualizada.TerminoHora.trim() : '',
        dataPrevista: ordemAtualizada.DataPrev,
        horaPrevista: ordemAtualizada.HoraPrev ? ordemAtualizada.HoraPrev.trim() : '',
        tempoServico: ordemAtualizada.TempoServ ? ordemAtualizada.TempoServ.trim() : '',
        observacoes: ordemAtualizada.Observacoes || '',
        usuarioId: ordemAtualizada.Usuario,
        nomeUsuario: ordemAtualizada.NomeUsuario ? ordemAtualizada.NomeUsuario.trim() : '',
        itemChecklistId: ordemAtualizada.ItemChecklist,
        itemChecklistDescricao: ordemAtualizada.ItemChecklistDescricao ? ordemAtualizada.ItemChecklistDescricao.trim() : ''
      };

      // Adicionar informações do veículo se disponíveis
      if (ordemAtualizada.VeiculoFrota && ordemAtualizada.PlacaVeiculo) {
        ordemFormatada.veiculo = {
          id: ordemAtualizada.VeiculoFrota,
          placa: ordemAtualizada.PlacaVeiculo.trim(),
          descricao: ordemAtualizada.DescricaoVeiculo ? ordemAtualizada.DescricaoVeiculo.trim() : ''
        };
      }

      // Adicionar ID do MovFrota se disponível
      if (ordemAtualizada.MovFrota) {
        ordemFormatada.movFrotaId = ordemAtualizada.MovFrota;
      }

      return {
        message: 'Observação adicionada com sucesso',
        ordem: ordemFormatada
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao adicionar observação'
      });
    }
  },

  /**
   * Atualiza a data e hora previstas para conclusão da OS
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados da ordem atualizada
   */
  atualizarDataPrevista: async (request, reply) => {
    const { id } = request.params;
    const { dataPrevista, horaPrevista } = request.body;

    // Verificar se ambos os campos estão vazios (remoção da data prevista)
    // ou se ambos estão preenchidos (definição da data prevista)
    const removendoDataPrevista = !dataPrevista && !horaPrevista;
    const definindoDataPrevista = dataPrevista && horaPrevista;

    // Se não estiver removendo nem definindo corretamente, retornar erro
    if (!removendoDataPrevista && !definindoDataPrevista) {
      return reply.code(400).send({
        error: 'Ambos os campos (data e hora) devem ser preenchidos ou ambos devem estar vazios'
      });
    }

    try {
      // Verificar se a ordem existe
      const checkQuery = `SELECT * FROM OS WHERE Id = @id`;
      const ordens = await executeQuery(checkQuery, { id });

      if (ordens.length === 0) {
        return reply.code(404).send({
          error: 'Ordem de serviço não encontrada'
        });
      }

      // Obter o ID do usuário atual a partir do token JWT
      const usuarioId = request.user.id;

      // Converter a data para o formato do SQL Server (YYYY-MM-DD) se estiver definindo a data
      let dataFormatada = null;
      if (definindoDataPrevista) {
        try {
          // Verificar se a data está no formato brasileiro (DD/MM/YYYY)
          if (dataPrevista.includes('/')) {
            const partes = dataPrevista.split('/');
            if (partes.length === 3) {
              dataFormatada = `${partes[2]}-${partes[1]}-${partes[0]}`;
            } else {
              dataFormatada = dataPrevista;
            }
          } else {
            dataFormatada = dataPrevista;
          }
        } catch (error) {
          dataFormatada = dataPrevista;
        }
      }

      // Atualizar a data e hora previstas
      const updateQuery = `
        UPDATE OS SET
          DataPrev = @dataPrevista,
          HoraPrev = @horaPrevista,
          Usuario = @usuarioId,
          UsuarioData = DATEADD(HOUR, -3, GETUTCDATE()),
          UsuarioHora = FORMAT(DATEADD(HOUR, -3, GETUTCDATE()), 'HH:mm')
        WHERE Id = @id;

        SELECT
          o.Id,
          o.Descricao,
          o.Abertura,
          o.AberHora,
          o.ColAceite,
          o.AceiteData,
          o.AceiteHora,
          o.Status,
          o.TerminoData,
          o.TerminoHora,
          o.DataPrev,
          o.HoraPrev,
          o.TempoServ,
          o.Observacoes,
          o.Usuario,
          o.VeiculoFrota,
          o.MovFrota,
          o.ItemChecklist,
          c.Nome AS NomeColaborador,
          c.Matricula AS MatriculaColaborador,
          u.Usuario AS NomeUsuario,
          f.Placa AS PlacaVeiculo,
          f.Descricao AS DescricaoVeiculo,
          cl.Descricao AS ItemChecklistDescricao
        FROM OS o
        LEFT JOIN Colaboradores c ON o.ColAceite = c.Id
        LEFT JOIN Usuarios u ON o.Usuario = u.Id
        LEFT JOIN Frota f ON o.VeiculoFrota = f.Id
        LEFT JOIN CheckList cl ON o.ItemChecklist = cl.Id
        WHERE o.Id = @id;
      `;

      const result = await executeQuery(updateQuery, {
        id,
        dataPrevista: dataFormatada,
        horaPrevista: removendoDataPrevista ? null : horaPrevista,
        usuarioId
      });

      if (!result || !result[0]) {
        throw new Error('Falha ao atualizar data prevista');
      }

      const ordemAtualizada = result[0];

      // Formatar os campos
      const ordemFormatada = {
        id: ordemAtualizada.Id,
        descricao: ordemAtualizada.Descricao ? ordemAtualizada.Descricao.trim() : '',
        dataAbertura: ordemAtualizada.Abertura,
        horaAbertura: ordemAtualizada.AberHora ? ordemAtualizada.AberHora.trim() : '',
        colaboradorId: ordemAtualizada.ColAceite,
        nomeColaborador: ordemAtualizada.NomeColaborador ? ordemAtualizada.NomeColaborador.trim() : '',
        matriculaColaborador: ordemAtualizada.MatriculaColaborador ? ordemAtualizada.MatriculaColaborador.trim() : '',
        dataAceite: ordemAtualizada.AceiteData,
        horaAceite: ordemAtualizada.AceiteHora ? ordemAtualizada.AceiteHora.trim() : '',
        status: ordemAtualizada.Status ? ordemAtualizada.Status.trim() : '',
        statusDescricao: getStatusDescricao(ordemAtualizada.Status),
        dataTermino: ordemAtualizada.TerminoData,
        horaTermino: ordemAtualizada.TerminoHora ? ordemAtualizada.TerminoHora.trim() : '',
        dataPrevista: ordemAtualizada.DataPrev,
        horaPrevista: ordemAtualizada.HoraPrev ? ordemAtualizada.HoraPrev.trim() : '',
        tempoServico: ordemAtualizada.TempoServ ? ordemAtualizada.TempoServ.trim() : '',
        observacoes: ordemAtualizada.Observacoes || '',
        usuarioId: ordemAtualizada.Usuario,
        nomeUsuario: ordemAtualizada.NomeUsuario ? ordemAtualizada.NomeUsuario.trim() : '',
        itemChecklistId: ordemAtualizada.ItemChecklist,
        itemChecklistDescricao: ordemAtualizada.ItemChecklistDescricao ? ordemAtualizada.ItemChecklistDescricao.trim() : ''
      };

      // Adicionar informações do veículo se disponíveis
      if (ordemAtualizada.VeiculoFrota && ordemAtualizada.PlacaVeiculo) {
        ordemFormatada.veiculo = {
          id: ordemAtualizada.VeiculoFrota,
          placa: ordemAtualizada.PlacaVeiculo.trim(),
          descricao: ordemAtualizada.DescricaoVeiculo ? ordemAtualizada.DescricaoVeiculo.trim() : ''
        };
      }

      // Adicionar ID do MovFrota se disponível
      if (ordemAtualizada.MovFrota) {
        ordemFormatada.movFrotaId = ordemAtualizada.MovFrota;
      }

      return {
        message: removendoDataPrevista
          ? 'Data prevista removida com sucesso'
          : 'Data prevista atualizada com sucesso',
        ordem: ordemFormatada
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao atualizar data prevista'
      });
    }
  },

  /**
   * Atualiza o tempo de serviço da OS
   * @param {Object} request - Objeto de requisição Fastify
   * @param {Object} reply - Objeto de resposta Fastify
   * @returns {Object} Dados da ordem atualizada
   */
  atualizarTempoServico: async (request, reply) => {
    const { id } = request.params;
    const { tempoServico } = request.body;
    const usuarioId = request.user.id;

    try {
      // Verificar se a ordem existe
      const checkQuery = `SELECT * FROM OS WHERE Id = @id`;
      const ordens = await executeQuery(checkQuery, { id });

      if (ordens.length === 0) {
        return reply.code(404).send({
          error: 'Ordem de serviço não encontrada'
        });
      }

      // Validar o formato do tempo de serviço (HH:MM)
      const regexHora = /^([0-1][0-9]|2[0-3]):([0-5][0-9])$/;
      if (!regexHora.test(tempoServico)) {
        return reply.code(400).send({
          error: 'O tempo de serviço deve estar no formato HH:MM'
        });
      }

      // Atualizar o tempo de serviço
      const updateQuery = `
        UPDATE OS SET
          TempoServ = @tempoServico,
          Usuario = @usuarioId,
          UsuarioData = DATEADD(HOUR, -3, GETUTCDATE()),
          UsuarioHora = FORMAT(DATEADD(HOUR, -3, GETUTCDATE()), 'HH:mm')
        WHERE Id = @id;

        SELECT
          o.Id,
          o.Descricao,
          o.Abertura,
          o.AberHora,
          o.ColAceite,
          o.AceiteData,
          o.AceiteHora,
          o.Status,
          o.TerminoData,
          o.TerminoHora,
          o.DataPrev,
          o.HoraPrev,
          o.TempoServ,
          o.Observacoes,
          o.Usuario,
          o.VeiculoFrota,
          o.MovFrota,
          o.ItemChecklist,
          c.Nome AS NomeColaborador,
          c.Matricula AS MatriculaColaborador,
          u.Usuario AS NomeUsuario,
          f.Placa AS PlacaVeiculo,
          f.Descricao AS DescricaoVeiculo,
          cl.Descricao AS ItemChecklistDescricao
        FROM OS o
        LEFT JOIN Colaboradores c ON o.ColAceite = c.Id
        LEFT JOIN Usuarios u ON o.Usuario = u.Id
        LEFT JOIN Frota f ON o.VeiculoFrota = f.Id
        LEFT JOIN CheckList cl ON o.ItemChecklist = cl.Id
        WHERE o.Id = @id;
      `;

      const result = await executeQuery(updateQuery, {
        id,
        tempoServico,
        usuarioId
      });

      if (!result || !result[0]) {
        throw new Error('Falha ao atualizar tempo de serviço');
      }

      const ordemAtualizada = result[0];

      // Formatar os campos
      const ordemFormatada = {
        id: ordemAtualizada.Id,
        descricao: ordemAtualizada.Descricao ? ordemAtualizada.Descricao.trim() : '',
        dataAbertura: ordemAtualizada.Abertura,
        horaAbertura: ordemAtualizada.AberHora ? ordemAtualizada.AberHora.trim() : '',
        colaboradorId: ordemAtualizada.ColAceite,
        nomeColaborador: ordemAtualizada.NomeColaborador ? ordemAtualizada.NomeColaborador.trim() : '',
        matriculaColaborador: ordemAtualizada.MatriculaColaborador ? ordemAtualizada.MatriculaColaborador.trim() : '',
        dataAceite: ordemAtualizada.AceiteData,
        horaAceite: ordemAtualizada.AceiteHora ? ordemAtualizada.AceiteHora.trim() : '',
        status: ordemAtualizada.Status ? ordemAtualizada.Status.trim() : '',
        statusDescricao: getStatusDescricao(ordemAtualizada.Status),
        dataTermino: ordemAtualizada.TerminoData,
        horaTermino: ordemAtualizada.TerminoHora ? ordemAtualizada.TerminoHora.trim() : '',
        dataPrevista: ordemAtualizada.DataPrev,
        horaPrevista: ordemAtualizada.HoraPrev ? ordemAtualizada.HoraPrev.trim() : '',
        tempoServico: ordemAtualizada.TempoServ ? ordemAtualizada.TempoServ.trim() : '',
        observacoes: ordemAtualizada.Observacoes || '',
        usuarioId: ordemAtualizada.Usuario,
        nomeUsuario: ordemAtualizada.NomeUsuario ? ordemAtualizada.NomeUsuario.trim() : '',
        itemChecklistId: ordemAtualizada.ItemChecklist,
        itemChecklistDescricao: ordemAtualizada.ItemChecklistDescricao ? ordemAtualizada.ItemChecklistDescricao.trim() : ''
      };

      // Adicionar informações do veículo se disponíveis
      if (ordemAtualizada.VeiculoFrota && ordemAtualizada.PlacaVeiculo) {
        ordemFormatada.veiculo = {
          id: ordemAtualizada.VeiculoFrota,
          placa: ordemAtualizada.PlacaVeiculo.trim(),
          descricao: ordemAtualizada.DescricaoVeiculo ? ordemAtualizada.DescricaoVeiculo.trim() : ''
        };
      }

      // Adicionar ID do MovFrota se disponível
      if (ordemAtualizada.MovFrota) {
        ordemFormatada.movFrotaId = ordemAtualizada.MovFrota;
      }

      // Log para depuração
      console.log('Ordem formatada:', ordemFormatada);

      return {
        message: 'Tempo de serviço atualizado com sucesso',
        ordem: ordemFormatada
      };
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        error: 'Erro ao atualizar tempo de serviço'
      });
    }
  }
};

/**
 * Retorna a descrição do status com base no código
 * @param {string} statusCode - Código do status
 * @returns {string} Descrição do status
 */
function getStatusDescricao(statusCode) {
  if (!statusCode) return 'Desconhecido';

  switch (statusCode.trim()) {
    case '1': return 'Aguardando aceite';
    case '2': return 'Em execução';
    case '3': return 'Aguardando peça';
    case '4': return 'Finalizada';
    case '5': return 'Pendente';
    case '6': return 'Concluída';
    default: return 'Desconhecido';
  }
}

module.exports = oficinaController;