/**
 * Utilitários para validação e processamento de status de veículos
 */

/**
 * Função utilitária para verificar se um status está OK
 * Aceita '1', 'OK' ou true como valores válidos
 * @param {string|boolean} status - Status a ser verificado
 * @returns {boolean} - true se o status estiver OK, false caso contrário
 */
const isStatusOk = (status) => {
  return status === '1' || status === 'OK' || status === true;
};

/**
 * Processa dados de veículo para converter status para boolean e formatar campos
 * @param {Object} veiculo - Dados do veículo
 * @returns {Object} - Veículo processado com status como boolean
 */
const processarVeiculo = (veiculo) => {
  if (!veiculo) return null;

  return {
    ...veiculo,
    // Formatar campos de texto removendo espaços
    Placa: veiculo.Placa ? veiculo.Placa.trim() : '',
    DescricaoVeiculo: veiculo.DescricaoVeiculo ? veiculo.DescricaoVeiculo.trim() : '',
    TipoVeiculo: veiculo.TipoVeiculo ? veiculo.TipoVeiculo.trim() : '',
    NomeMotorista: veiculo.NomeMotorista ? veiculo.NomeMotorista.trim() : '',
    MatriculaMotorista: veiculo.MatriculaMotorista ? veiculo.MatriculaMotorista.trim() : '',
    
    // Converter status para boolean no backend - validação centralizada
    StatusPosto: veiculo.StatusPosto !== undefined ? isStatusOk(veiculo.StatusPosto) : undefined,
    StatusLav: veiculo.StatusLav !== undefined ? isStatusOk(veiculo.StatusLav) : undefined,
    StatusVist: veiculo.StatusVist !== undefined ? isStatusOk(veiculo.StatusVist) : undefined
  };
};

/**
 * Processa uma lista de veículos
 * @param {Array} veiculos - Lista de veículos
 * @returns {Array} - Lista de veículos processados
 */
const processarVeiculos = (veiculos) => {
  if (!Array.isArray(veiculos)) return [];
  return veiculos.map(processarVeiculo);
};

module.exports = {
  isStatusOk,
  processarVeiculo,
  processarVeiculos
};
